<?php

namespace App\ProcessTask\Course;

use App\ProcessTask\Course\Payloads\AddUnitToCoursePayload;
use App\Services\SyncUnitsSetupService;
use Closure;

class SyncToLegacy
{
    public function handle(AddUnitToCoursePayload $payload, Closure $next): AddUnitToCoursePayload
    {
        // if the subject was removed
        if ($payload->subject) {
            if ($payload->unitToSave->Added) {
                SyncUnitsSetupService::SyncCourseSubjectToOldSetup($payload->subject, true, true);
            } else {
                SyncUnitsSetupService::DeleteCourseSubjectFromOldSetup($payload->subject);
            }
        }

        return $next($payload);
    }
}
