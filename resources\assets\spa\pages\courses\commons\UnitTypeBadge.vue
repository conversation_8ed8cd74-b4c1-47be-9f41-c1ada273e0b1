<template>
    <div :class="getClass">
        {{ getText }}
    </div>
</template>
<script>
export default {
    name: 'UnitListItem',
    props: {
        type: String,
        size: { type: String, default: 'normal' },
        text: { type: String, default: null },
        prefix: { type: String, default: null },
        suffix: { type: String, default: null },
        item: { type: String, default: 'Unit' },
    },
    computed: {
        getClass() {
            const commonCls = 'min-w-[80px] rounded-md border-0 px-4 py-1 text-xs h-fit';
            if (this.type == 'core') {
                return `${commonCls} bg-primary-blue-100 text-primary-blue-800`;
            } else {
                return `${commonCls} self-baseline bg-gray-100 text-gray-800`;
            }
        },
        getText() {
            let badgeText = null;
            if (this.text) {
                badgeText = this.text;
            } else {
                if (this.type == 'core') {
                    badgeText = `Core ${this.item}`;
                } else {
                    badgeText = `Elective ${this.item}`;
                }
            }
            const preText = this.prefix ? `${this.prefix} ` : '';
            const postText = this.suffix ? `${this.suffix} ` : '';
            return `${preText}${badgeText}${postText}`;
        },
    },
};
</script>
