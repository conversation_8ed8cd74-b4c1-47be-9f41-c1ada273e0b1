<template>
    <form class="k-form" @submit.prevent="handleSubmit" method="post" novalidate>
        <Card :pt="{ root: 'rounded-lg p-0 md:p-0' }">
            <template #content>
                <div class="max-w-xl space-y-4 px-6 pt-6">
                    <div v-if="!isProfile">
                        <div class="mb-1 font-medium text-gray-700">
                            Would you like to create a Major set for this course?
                        </div>
                        <RadioGroup
                            :data-items="templateCreateOptions"
                            :layout="'horizontal'"
                            v-model="createTemplateOption"
                        />
                    </div>
                    <div v-if="formReady" class="space-y-4">
                        <fieldset v-if="coursesData">
                            <div class="space-y-4">
                                <div class="flex space-x-2">
                                    <div class="w-5/6 space-y-1">
                                        <div class="font-medium text-gray-700">Select Course</div>
                                        <DropDownList
                                            :data-items="getCoursesList"
                                            :text-field="'title'"
                                            :data-item-key="'id'"
                                            :value-field="'id'"
                                            :value-primitive="true"
                                            :value="getCurrentCourseId"
                                            @change="handleDropdownChange"
                                            :popup-settings="{
                                                className: 'tw-width-auto',
                                                animate: false,
                                            }"
                                            :disabled="isCourseDropdownDisabled"
                                        ></DropDownList>
                                    </div>
                                </div>
                                <span
                                    class="text-red-500"
                                    v-if="
                                        v$.templateData.course_id &&
                                        v$.templateData.course_id.$dirty &&
                                        v$.templateData.course_id.$error
                                    "
                                >
                                    Select a Course
                                </span>
                            </div>
                        </fieldset>
                        <div v-if="getCurrentCourseId" class="space-y-4">
                            <fieldset>
                                <div class="space-y-2">
                                    <div class="mb-2 flex space-x-2">
                                        <div class="w-5/6 space-y-1">
                                            <div class="font-medium text-gray-700">
                                                Enter Course Major Name
                                            </div>
                                            <TextBox
                                                v-model="templateData.template_name"
                                                :placeholder="'Course Major Name'"
                                                :disabled="isMasterTemplate"
                                                @blur="v$.templateData.template_name.$commit;"
                                            />
                                        </div>
                                        <div class="flex w-1/6 flex-col justify-end">
                                            <span class="align-bottom text-gray-800">{{
                                                getCurrentCourseCode
                                            }}</span>
                                        </div>
                                    </div>
                                    <div class="mb-4">
                                        <span class="mr-1 text-gray-500"
                                            >Course Major name is being saved as::</span
                                        >
                                        <span class="text-gray-700">{{ getTemplateFullName }}</span>
                                    </div>
                                    <span
                                        class="text-red-500"
                                        v-if="
                                            v$.templateData.template_name &&
                                            v$.templateData.template_name.$dirty &&
                                            v$.templateData.template_name.$error
                                        "
                                    >
                                        Provide the name of the major
                                    </span>
                                </div>
                            </fieldset>
                            <fieldset>
                                <div class="flex space-x-4">
                                    <div class="w-1/2 space-y-1">
                                        <div class="font-medium text-gray-700">
                                            Number of Core {{ isHigherEd ? 'Subjects' : 'Units' }}
                                        </div>
                                        <NumericTextBox
                                            v-model="templateData.no_of_core_subject"
                                            :placeholder="`Core ${isHigherEd ? 'Subjects' : 'Units'}`"
                                            :default-value="0"
                                            :spinners="false"
                                            :step="1"
                                            :min="0"
                                            :max="100"
                                            :disabled="isCoreUnitsFieldDisabled"
                                            @blur="v$.templateData.no_of_core_subject.$commit"
                                        />
                                    </div>
                                    <div class="w-1/2 space-y-1">
                                        <div class="font-medium text-gray-700">
                                            Number of Elective
                                            {{ isHigherEd ? 'Subjects' : 'Units' }}
                                        </div>
                                        <NumericTextBox
                                            v-model="templateData.no_of_elective_subject"
                                            :placeholder="`Elective ${isHigherEd ? 'Subjects' : 'Units'}`"
                                            :default-value="0"
                                            :spinners="false"
                                            :step="1"
                                            :min="0"
                                            :max="100"
                                            @blur="v$.templateData.no_of_elective_subject.$commit;"
                                        />
                                    </div>
                                </div>
                                <span class="text-red-500" v-if="hasPackagingError">
                                    Provide unit packaging rules for this major
                                </span>
                            </fieldset>
                            <fieldset>
                                <div class="flex justify-between">
                                    <label for="courseDefault" class="font-medium text-gray-700">
                                        Set To Default
                                    </label>
                                    <Switch
                                        @change="setCourseDefault"
                                        :checked="templateData.set_default"
                                        id="courseDefault"
                                        :disabled="isMasterTemplate"
                                    />
                                </div>
                            </fieldset>
                            <fieldset>
                                <div class="flex justify-between">
                                    <label for="setCourseActive" class="font-medium text-gray-700">
                                        Set To Active
                                    </label>
                                    <Switch
                                        @change="setCourseActive"
                                        :checked="templateData.set_active"
                                        id="setCourseActive"
                                        :disabled="isMasterTemplate"
                                    />
                                </div>
                            </fieldset>
                        </div>
                        <div v-else class="mt-6 rounded-md border p-4">
                            <div class="text-gray-600">
                                To proceeed further, select the course you are trying to create the
                                major for.
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="rounded-b-md bg-gray-50 px-6 py-3"
                    v-if="getCurrentCourseId && showSaveButton && formReady"
                >
                    <div class="flex w-full items-center justify-end gap-4">
                        <Button :size="'sm'" :variant="'secondary'" @click="cancelForm">
                            <span class="px-6 text-sm leading-4 text-gray-500"> Cancel </span>
                        </Button>
                        <Button
                            type="submit"
                            :size="'sm'"
                            :variant="'primary'"
                            :class="{ disabled: dataChanged < 1 || isSubmitting }"
                            :loading="isSubmitting"
                            :loadingText="'Saving Template...'"
                            :disabled="dataChanged < 1"
                        >
                            <span class="px-6 text-sm leading-4 text-white"> Save </span>
                        </Button>
                    </div>
                </div>
            </template>
        </Card>
    </form>
</template>
<script>
/* 
this form is to create and edit templates
this form will have a save button which is only used in the course setting page
in the course template page; the course template data will be handled by the parent component
*/
import { TextBox, Switch, NumericTextBox, RadioGroup } from '@progress/kendo-vue-inputs';
import Button from '@spa/components/Buttons/Button';
import { useVuelidate } from '@vuelidate/core';
import { required, minLength, minValue, requiredIf } from '@vuelidate/validators';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import Card from '@spa/components/Card/Card.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';

export default {
    setup() {
        const v$ = useVuelidate();
        const globalLoaderStore = useLoaderStore();
        // onMounted(() => {});
        return { v$, globalLoaderStore };
    },
    emits: ['saved'],
    components: {
        TextBox,
        Switch,
        NumericTextBox,
        Button,
        DropDownList,
        RadioGroup,
        Card,
    },
    validations() {
        return {
            templateData: {
                template_name: { required },
                no_of_core_subject: {
                    requiredIf: requiredIf(!this.no_of_elective_subject),
                    minValue: 1,
                },
                no_of_elective_subject: {
                    requiredIf: requiredIf(!this.no_of_core_subject),
                    minValue: 1,
                },
            },
        };
    },
    data() {
        return {
            dataChanged: 0,
            isSubmitting: false,
            currentCourse: [],
            templateData: {
                id: null,
                course_id: null,
                template_name: '',
                no_of_core_subject: null,
                no_of_elective_subject: null,
                set_default: false,
                set_active: true,
                is_master_template: false,
            },
            templateCreateOptions: [
                { label: 'Yes', value: 'yes' },
                { label: 'No', value: 'no' },
            ],
            createTemplateOption: 'no',
        };
    },
    props: {
        course: { type: [Object, Array], default: [] },
        coursesData: { type: [Array], default: null },
        formData: { type: [Object, Array], default: null },
        consumer: { type: String, default: 'settings' },
        isProfile: { type: Boolean, default: false },
        hasTemplates: { type: Boolean, default: false },
        autosaving: { type: Boolean, default: false },
    },
    async mounted() {
        try {
            await this.loadTemplateFormData(this.formData, 1);
            this.currentCourse = this.course || [];
            this.createTemplateOption = this.isProfile || this.hasTemplates ? 'yes' : 'no';
        } finally {
            this.dataChanged = 0;
        }
    },
    computed: {
        isHigherEd() {
            return this.isCourseHigherEd(this.course?.course_type_id || null);
        },
        allowCourseChange() {
            return this.coursesData?.editatable || false;
        },
        getCoursesList() {
            return this.coursesData?.courses || [];
        },
        getCurrentCourseId() {
            const course = this.currentCourse || [];
            return course.id ?? 0;
        },
        getCurrentCourseCode() {
            const course = this.currentCourse || [];
            return course.course_code ?? '';
        },
        getTemplateFullName() {
            return `${this.templateData.template_name}_${this.getCurrentCourseCode}`;
        },
        isMasterTemplate() {
            const masterTemplate = !!(this.templateData.is_master_template || false);
            if (masterTemplate) this.templateData.set_active = 1;
            return masterTemplate;
        },
        isCoreUnitsFieldDisabled() {
            return false;
            return !this.isMasterTemplate;
        },
        isCourseDropdownDisabled() {
            return this.coursesData?.editatable === false;
        },
        hasPackagingError() {
            const coredirty =
                this.v$.templateData.no_of_core_subject &&
                this.v$.templateData.no_of_core_subject.$dirty &&
                this.v$.templateData.no_of_core_subject.$error;
            const electivedirty =
                this.v$.templateData.no_of_elective_subject &&
                this.v$.templateData.no_of_elective_subject.$dirty &&
                this.v$.templateData.no_of_elective_subject.$error;
            return coredirty || electivedirty;
        },
        showSaveButton() {
            return this.consumer !== 'templates';
        },
        formReady() {
            return this.isProfile || this.createTemplateOption === 'yes';
        },
    },
    methods: {
        loadTemplateFormData(data, resetChangeCount = 0) {
            this.templateData.id = data.id || null;
            this.templateData.template_name = data.template_name || '';
            this.templateData.course_id = this.getCurrentCourseId;
            this.templateData.no_of_core_subject = data.no_of_core_subject || null;
            this.templateData.no_of_elective_subject = data.no_of_elective_subject || null;
            const isdefault = data.set_default && data.set_default == 1;
            this.templateData.set_default = isdefault;
            const active = data.set_active && data.set_active == 1;
            this.templateData.set_active = active;
            this.templateData.is_master_template = data.is_master_template || false;
            if (resetChangeCount) {
                setTimeout(() => {
                    this.dataChanged = 0;
                }, 10);
            }
        },
        cancelForm() {
            this.$emit('cancel');
        },
        handleSubmit(e) {
            const formData = {
                ...this.templateData,
                course_id: this.getCurrentCourseId || null,
                consumer: this.consumer,
                autosaving: this.autosaving,
                profilemode: this.isProfile,
            };
            this.isSubmitting = true;
            this.v$.$touch();
            if (this.v$.$invalid) {
                this.isSubmitting = false;
                return false;
            }
            if (this.dataChanged < 1) {
                /* 
                some time this method can be triggered by the parent component to automatically save the data changed;
                if if no data is changed not need to do the serverside call
                so return save method with the data asusual
                */
                this.$emit('cancel');
                return;
            }
            this.globalLoaderStore.startContextLoading('loading-template-units');
            $http
                .post(route('spa.coursetemplates.save'), formData)
                .then((resp) => {
                    if (resp.success) {
                        this.dataChanged = 1;
                        this.showPopupSuccess(resp.message, 'Success');
                        this.$emit('saved', resp);
                    }
                })
                .finally(() => {
                    this.globalLoaderStore.stopContextLoading('loading-template-units');
                });
        },
        setCourseDefault(e) {
            this.templateData.set_default = e.value;
            if (e.value == true) {
                this.setCourseActive(e);
            }
        },
        setCourseActive(e) {
            this.templateData.set_active = e.value;
            if (e.value == false) {
                this.setCourseDefault(e);
            }
        },
        handleDropdownChange(e) {
            const courseId = e.value || null;
            const selectedCourse = this.getCoursesList.find((item) => item.id === courseId);
            this.currentCourse = {
                id: selectedCourse.id || null,
                course_code: selectedCourse.code || '',
                course_name: selectedCourse.title || '',
            };
            return;
        },
    },
    watch: {
        formData: {
            async handler(newValue, oldValue) {
                try {
                    await this.loadTemplateFormData(newValue, 1);
                } finally {
                    this.dataChanged = 0;
                }
            },
            deep: true,
        },
        templateData: {
            handler(newValue, oldValue) {
                this.dataChanged++;
            },
            deep: true,
        },
        course: {
            handler(newValue, oldValue) {
                this.currentCourse = newValue;
            },
            deep: true,
        },
        createTemplateOption(newval, oldval) {
            if (!this.isProfile) {
                this.$emit('decided', newval);
            }
        },
        autosaving(newval) {
            if (newval === true) {
                this.handleSubmit();
            }
        },
    },
};
</script>
