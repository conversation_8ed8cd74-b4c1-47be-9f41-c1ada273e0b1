<?php

namespace App\Model\v2;

use App\Services\CourseUnitsService;
use App\Services\SyncUnitsSetupService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Support\Traits\CreaterUpdaterTrait;

class CourseSubjects extends Model
{
    use CreaterUpdaterTrait;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'rto_course_subjects';

    protected $fillable = [
        'course_id',
        'subject_code',
        'subject_name',
        'subject_type',
        'grading_type',
        'max_marks_allow',
        'contact_hours',
        'level',
        'credit_point',
        'is_long_indicator',
        'is_assessment',
        'discipline_broad_type',
        'discipline_narrow_type',
        'discipline_narrow_sub_type',
        'course_stage',
        'subject_fee',
        'domestic_subject_fee',
        'delivery_mode',
        'internal',
        'external',
        'workplace_based_delivery',
        'EFTSL_study_load',
        'is_active',
        'inc_in_certificate',
        'display_order',
        'synced_subject',
        'synced_for',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    protected static function booted()
    {
        static::created(function ($subject) {
            SyncUnitsSetupService::SyncCourseSubjectToOldSetup($subject, true, true);
        });
        static::updated(function ($subject) {
            SyncUnitsSetupService::SyncCourseSubjectToOldSetup($subject, true, true);
        });
        static::deleting(function ($subject) {
            SyncUnitsSetupService::DeleteCourseSubjectFromOldSetup($subject);
        });
    }

    public function subject_units()
    {
        return $this->hasMany(SubjectUnits::class, 'course_subject_id', 'id')->where('status', 1);
    }

    public function subject_unit()
    {
        return $this->hasOne(SubjectUnits::class, 'course_subject_id', 'id')->where('status', 1);
    }

    public function course_subject()
    {
        return $this->hasOne(CourseSubject::class, 'id', 'synced_for');
    }

    public function course()
    {
        return $this->belongsTo(Courses::class, 'course_id', 'id');
    }

    /*
    synced subject
    */
    public function subject()
    {
        return $this->belongsTo(Subject::class, 'synced_subject', 'id');
    }

    public function subjectEnrolments()
    {
        return $this->hasMany(StudentSubjectEnrolment::class, 'subject_id', 'synced_subject');
    }

    public function canBeUpdated($updateData = [])
    {
        // currently return true for every request
        // as new system is not in use
        // after implementation need to add logic here to check if subject is editable or not
        // Harish chandra kharel 11th April, 2024
        // check if subject has batches;
        // units can not be changed
        // check the units to be added and check the units if are in other batches
        // units can not be moved to or from those subjects if the subject has batches created
        $subjectId = $this->synced_subject ?? null;
        $subjectEnrolments = $this->subjectEnrolments()->where('course_id', $this->course_id)->count();
        if ($subjectEnrolments > 0) {
            return false;
        }

        // return false if there are batch created for the subject
        return ! (Timetable::where(['subject_id' => $subjectId])->count() > 0);
    }

    public static function import($data = [])
    {
        $subjectCode = $data['subject_code'] ?? null;
        $courseId = $data['course_id'] ?? null;
        if ($subjectCode && $courseId) {
            $existingSubject = self::where('subject_code', '=', $subjectCode)->where('course_id', '=', $courseId)->first();
            $existingId = $existingSubject->id ?? null;
            if (! $existingId) {
                $deliveryMode = $data['delivery_mode'] ?? 'NNN';
                $deliverModes = str_split($deliveryMode);
                $deliverMode_internal = $deliverModes[0] ?? 'N';
                $deliverMode_external = $deliverModes[1] ?? 'N';
                $deliverMode_wpd = $deliverModes[2] ?? 'N';
                $tuitionFees = $data['subject_fee'] ?? 0;
                $domesticTuitionFees = $data['domestic_subject_fee'] ?? $tuitionFees;
                $importData = [
                    'course_id' => $data['course_id'] ?? '',
                    'subject_code' => $data['subject_code'] ?? '',
                    'subject_name' => $data['subject_name'] ?? '',
                    'subject_type' => $data['subject_type'] ?? 'None',
                    'grading_type' => $data['grading_type'] ?? null,
                    'max_marks_allow' => $data['max_marks_allow'] ?? null,
                    'contact_hours' => $data['contact_hours'] ?? null,
                    'level' => $data['level'] ?? null,
                    'credit_point' => $data['credit_point'] ?? null,
                    'is_long_indicator' => $data['is_long_indicator'] ?? null,
                    'is_assessment' => $data['is_assessment'] ?? null,
                    'discipline_broad_type' => $data['discipline_broad_type'] ?? null,
                    'discipline_narrow_type' => $data['discipline_narrow_type'] ?? null,
                    'discipline_narrow_sub_type' => $data['discipline_narrow_sub_type'] ?? null,
                    'course_stage' => $data['course_stage'] ?? '',
                    'subject_fee' => $tuitionFees,
                    'domestic_subject_fee' => $domesticTuitionFees,
                    'delivery_mode' => $data['delivery_mode'] ?? null,
                    'internal' => $deliverMode_internal,
                    'external' => $deliverMode_external,
                    'workplace_based_delivery' => $deliverMode_wpd,
                    'EFTSL_study_load' => $data['EFTSL_study_load'] ?? null,
                    'is_active' => $data['is_active'] ?? 0,
                    'inc_in_certificate' => $data['inc_in_certificate'] ?? 0,
                    'display_order' => $data['display_order'] ?? 1,
                    'created_by' => $data['created_by'] ?? null,
                    'updated_by' => $data['updated_by'] ?? null,
                    'created_at' => $data['created_at'] ?? null,
                    'updated_at' => $data['updated_at'] ?? null,
                ];

                return self::create($importData);
            } else {
                return $existingSubject;
            }
        }

        return false;
    }

    public static function syncSubject($subjectData = null)
    {
        $subjectCode = $subjectData['subject_code'] ?? '';
        $id = $subjectData['id'] ?? 0;
        $courseId = $subjectData['course_id'] ?? 0;
        $subject = null;
        if ($id) {
            $subject = CourseSubjects::where(['id' => $id, 'course_id' => $courseId])->first();
        }
        if (empty($subject)) {
            $subject = CourseSubjects::where(['subject_code' => $subjectCode, 'course_id' => $courseId])->first();
        }
        unset($subjectData['id']);
        if (empty($subject)) {
            $subject = new CourseSubjects($subjectData);
            $subject->save();
        } else {
            $subject->update($subjectData);
        }

        // dump($subject);
        return $subject;
    }

    public static function SaveSubject($inputData, $courseData, CourseTemplate $templateData)
    {
        $college_id = auth()->user()->college_id;
        // saperate the fields to be saved the data may be saved to subject, to unit or to course_unit
        $courseId = (int) $inputData['course_id'] ?? 0;
        $subjectId = (int) $inputData['id'] ?? 0;
        $subject = null;
        // if subjectId
        $update = false;
        $user_id = auth()->user()->id ?? 0;
        if ($subjectId) {
            $subject = self::with(['subject_units'])->find($subjectId);
            $update = true;
        } else {
            $subject = new self($inputData);
        }
        if (! $subject || empty($subject->getAttributes())) {
            return 'subjectnotfound';
        }
        $editable = $subject->canBeUpdated($inputData);

        if ($update) {
            $subject->update($inputData);
        } else {
            $subject->save();
        }
        if (! $subject->id) {
            return 'subjectnotsaved';
        }

        if ($subject->id > 0 && ! $editable) { // if subject is already created and has batches
            return 'hasenrollments';
        }

        // now go to the units
        // get all the units of the subject first
        $subjectUnits = $subject->subject_units ?? [];
        $addedUnits = $inputData['units'] ?? [];
        $existingCodes = $subjectUnits->pluck('unit_code')->count();
        $addedCodes = Arr::pluck($addedUnits, 'Code');
        $newCodes = count($addedCodes);
        $excludedUnits = collect();
        $unitsRemoved = 0;
        foreach ($subjectUnits as $ind => $currentUnit) {
            if (! in_array($currentUnit['unit_code'], $addedCodes)) {
                $excludedUnits[] = $currentUnit;
                $unitsRemoved++;
            }
        }
        // this unit has been removed from the previous list..
        $removed = ($excludedUnits->count() > 0) ? self::RemoveUnitsFromSubject($excludedUnits) : $excludedUnits;
        // unit master array;;
        $added = 0;
        foreach ($addedUnits as $addedunit) {
            $isTemporayUnit = isset($addedunit['temporary']) && $addedunit['temporary'] ?? false;
            $unit = $addedunit['unit_details'] ?? [];
            if (empty($unit)) {
                if ($isTemporayUnit) {
                    $unitService = new CourseUnitsService;
                    $unit = $unitService->saveCustomUnit($addedunit, $templateData); /* save the unit and get the unit data */
                }
                if (empty($unit)) {
                    continue;
                }
            }

            $filters = [
                'id' => $unit['id'],
                'unit_id' => $unit['unit_id'],
                'course_id' => $unit['course_id'],
            ];
            $unitToUpdate = SubjectUnits::where($filters)->first();
            $update = ['course_subject_id' => $subject->id];
            if ($unitToUpdate->course_subject_id && $unitToUpdate->synced_for == null) {
                $update['trigger_sync'] = time();
            }
            $updated = $unitToUpdate->update($update);
            $added += $updated ? 1 : 0;
        }

        if ($subject->synced_subject) {
            SubjectMaterial::CheckAndCreateSubjectFolder(Subject::find($subject->synced_subject));
        }
        if ($subject) {
            $templateData->mapSubjectToTemplate($subject, true);
        }

        return ($added > 0) ? true : 'nounitsadded';
    }

    /* function to remove one or many units from the subject */
    public static function RemoveUnitsFromSubject($units = null)
    {
        if (empty($units)) {
            return;
        }
        // $unitIds = Arr::pluck($units, 'id');
        // $unitsCollection = SubjectUnits::whereIn('id', $unitIds)->get();
        $units->each(function ($unit) {
            $unit->course_subject_id = null;
            $unit->save();
            SyncUnitsSetupService::DeleteUnitFromOldSetup($unit);
        });

        return $units;
    }

    /* function to remove subject from the course; this will deassociate all the units as well */
    public function DeleteFromCourse()
    {
        if (! $this->canBeUpdated()) {
            return 'hasenrollments';
        }
        // first remove all the units from the subject
        $units = $this->RemoveUnitsFromSubject($this->subject_units()->get());

        // now delete the subject
        return $this->delete();
    }

    /*
    This function deletes the subject and all the units associated with the subject
    This method deletes the records quietly so should be used after deletion of CourseSubject from the course Legacy
    */
    public function processForceDeletion()
    {
        $templates = CourseTemplate::where(['course_id' => $this->course_id])->pluck('id')->toArray();
        $units = $this->subject_units()->get();
        $units->each(function ($unit) use ($templates) {
            $unit->deleteQuietly();
            /* delete the units from the templates */
            CourseTemplateStructure::where(['unit_id' => $unit->id])->whereIn('course_template_id', $templates)->delete();
        });

        /* remove the subject from the templates */
        CourseTemplateSubject::where(['subject_id' => $this->id])->whereIn('course_template_id', $templates)->delete();

        return $this->deleteQuietly();
    }

    public function updateTemplateStructure(?CourseTemplate $template = null)
    {
        /*
        This function updates the template structure for the subject
        */
        /* check if the subject does exists in any on the templates */
        $currentAssignedTemplates = CourseTemplate::whereHas('templatesubjects', function ($query) {
            $query->where('subject_id', $this->id);
        })->where('course_id', $this->course_id)->orderByDesc('is_master_template')->orderByDesc('set_default')->get();
        $templatesFound = $currentAssignedTemplates->count();

        if (! $template) {
            if ($templatesFound == 1) {
                /* the subject is assigned to only one template so use that template to update */
                $template = $currentAssignedTemplates->first();
            } elseif ($templatesFound > 1) {
                /* the subject is assigned more than one template, so get the master template if exists */
                $template = $currentAssignedTemplates->where('is_master_template', 1)->first();
            } else {
                /* still no template found get the master template */
                $template = CourseTemplate::getTemplateData($this->course_id, null, true)->first();
            }
        }

        if (! $template) {
            return;
        }

        $template->mapSubjectToTemplate($this, true);

        return $template;
    }
}
