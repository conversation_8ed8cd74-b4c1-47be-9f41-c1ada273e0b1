<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Pest\Plugins\Only;
use Support\Traits\CreaterUpdaterTrait;

class Subject extends Model
{
    use CreaterUpdaterTrait;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'rto_subject';

    protected $reason_deny = false;

    protected $fillable = [
        'college_id',
        'course_type',
        'subject_code',
        'subject_name',
        'grading_type',
        'max_marks_allow',
        'contact_hours',
        'level',
        'credit_point',
        'is_long_indicator',
        'is_assessment',
        'discipline_broad_type',
        'discipline_narrow_type',
        'discipline_narrow_sub_type',
    ];

    protected static function booted()
    {
        static::created(function ($subject) {
            SubjectMaterial::CheckAndCreateSubjectFolder($subject);
        });
        static::updated(function ($subject) {
            SubjectMaterial::CheckAndCreateSubjectFolder($subject);
        });
    }

    public function gradingtype()
    {
        return $this->hasOne(GradingType::class, 'id', 'grading_type');
    }

    public function broadtype()
    {
        return $this->hasOne(BroadType::class, 'id', 'discipline_broad_type')->select(['id', 'broad_key', 'title']);
    }

    public function narrowtype()
    {
        return $this->hasOne(NarrowType::class, 'id', 'discipline_narrow_type')->select(['id', 'broad_id', 'narrow_key', 'title']);
    }

    public function subnarrowtype()
    {
        return $this->hasOne(SubNarrowType::class, 'id', 'discipline_narrow_sub_type')->select(['id', 'broad_id', 'narrow_id', 'sub_narrow_key', 'title']);
    }

    public function subjectUnits()
    {
        return $this->hasMany(UnitModule::class, 'subject_id', 'id');
    }

    public function timetables()
    {
        return $this->hasMany(Timetable::class, 'subject_id');
    }

    public function subjectEnrolments()
    {
        return $this->hasMany(StudentSubjectEnrolment::class, 'subject_id');
    }

    /*
    function to check if the current subject has batches assigned to it;
    will return the numebr of batches (generally the number of batch will be 1 in case present)
    */
    public function hasBatches()
    {
        $subjectId = $this->id ?? 0;
        if ($subjectId > 0) {
            return Timetable::where(['subject_id' => $subjectId])->count();
        }

        return 0;
    }

    // subject can only be updated if no batches/timetable has been created for the particular subject
    public function canBeUpdated($updates = null)
    {
        // get changed value from the model
        $updatedAttributes = $this->getChanges();
        // dd($updatedAttributes);
        $subjectId = $this->id ?? 0;
        if (! $updates) {
            return $this->hasBatches() === 0;
        }
        if ($subjectId > 0) {
            /* these fields can not be changed if the subject has batches into it */
            $checkFields = [
                'course_type',
                'subject_code',
                'subject_name',
                'grading_type',
                'max_marks_allow',
                'contact_hours',
                'level',
                'credit_point',
                'is_long_indicator',
                'is_assessment',
                'discipline_broad_type',
                'discipline_narrow_type',
                'discipline_narrow_sub_type',
            ];
            $changesDetected = false;
            foreach ($checkFields as $field) {
                // Check if the field value is different between the two objects
                if ($this->$field !== null && $this->$field != $updates->$field) {
                    // echo $field." ==>> ".$this->$field." !== ".$updates->$field."<br />";
                    $changesDetected = true;
                    break;
                }
            }

            return ! ($changesDetected && $this->hasBatches() > 0);
        }

        return false;
    }

    // subject can only be deleted if no batches/timetable has been created for the particular subject
    public function canBeDeleted()
    {
        // get changed value from the model
        $updatedAttributes = $this->getChanges();

        // dd($updatedAttributes);
        return $this->hasBatches() === 0;

    }

    public static function SaveSubject($inputData = [])
    {
        $inputData = (object) $inputData;
        $college_id = Auth::user()->college_id;
        $user_id = Auth::user()->id;
        // saperate the fields to be saved the data may be saved to subject, to unit or to course_unit
        $courseSubjectId = (int) $inputData->map_id ?? 0;
        $courseId = (int) $inputData->course_id ?? 0;
        $subjectId = (int) $inputData->id ?? 0;
        $unitsAdded = (int) $inputData->units ?? 0;
        $subject = null;
        // if subjectId
        if ($subjectId) {
            $subject = Subject::where('college_id', $college_id)->find($subjectId);
            $subject->updated_by = $user_id;
        } else {
            $subject = new Subject;
            $subject->college_id = $college_id;
            $subject->created_by = $user_id;
        }
        if (! $subject) {
            return 'subjectnotfound';
        }
        $editable = $subject->canBeUpdated($inputData);
        if ($subject->id > 0 && ! $editable) { // if subject is already created and has batches
            return 'hasbatches';
        }
        $subject->course_type = $inputData->course_type;
        $subject->subject_code = $inputData->subject_code;
        $subject->subject_name = $inputData->subject_name;
        $subject->grading_type = $inputData->grading_type;
        $subject->max_marks_allow = $inputData->max_marks_allow;
        $subject->contact_hours = $inputData->contact_hours;
        $subject->level = $inputData->level;
        $subject->credit_point = $inputData->credit_point;
        $subject->is_long_indicator = $inputData->is_long_indicator;
        $subject->is_assessment = $inputData->is_assessment;
        $subject->discipline_broad_type = $inputData->discipline_broad_type;
        $subject->discipline_narrow_type = $inputData->discipline_narrow_type;
        $subject->discipline_narrow_sub_type = $inputData->discipline_narrow_sub_type;
        $subject->save();

        if (! $subject->id) {
            return 'subjectnotsaved';
        }
        // map table master data
        $unitSubjectMapData = [
            'college_id' => $college_id,
            'course_id' => $courseId,
            'subject_id' => $subject->id,
            'course_stage' => $inputData->course_stage,
            'subject_type' => $inputData->subject_type,
            'subject_fee' => $inputData->subject_fee,
            'domestic_subject_fee' => $inputData->domestic_subject_fee,
            'unit_type' => $inputData->unit_type,
            'delivery_mode' => $inputData->delivery_mode,
            'EFTSL_study_load' => $inputData->EFTSL_study_load,
            'contact_hours' => $inputData->contact_hours,
            'is_active' => $inputData->status,
            'inc_in_certificate' => $inputData->inc_in_certificate,
            'created_by' => $user_id,
            'updated_by' => $user_id,
        ];
        // now map the subject with the course
        $mapped = $subject->MapCourseSubject($unitSubjectMapData);
        if (! $mapped) {
            return 'subjectnotmapped';
        } elseif ($mapped === 'hasbatches') {
            return $mapped;
        }
        // now go to the units
        // get all the units of the subject first
        $subjectUnits = UnitModule::select('id', 'unit_code')->where('subject_id', $subject->id)->get();
        $subjectUnits = UnitModule::where('subject_id', $subject->id)->get();
        // dd($subjectUnits);
        $addedUnits = $inputData->units;
        $existingCodes = $subjectUnits->pluck('unit_code')->count();
        $addedCodes = Arr::pluck($addedUnits, 'Code');
        $newCodes = count($addedCodes);
        $excludedUnits = [];
        $unitsRemoved = 0;
        foreach ($subjectUnits as $ind => $currentUnit) {
            if (! in_array($currentUnit['unit_code'], $addedCodes)) {
                $excludedUnits[] = $currentUnit;
                $unitsRemoved++;
            }
        }
        $unitsAltered = ($existingCodes > 0 && ($existingCodes != $newCodes || $unitsRemoved > 0));
        if ($unitsAltered && $subject->hasBatches()) {
            // if the subject has batches and some changes has been done to the units;
            // the action is not allowed
            // dd($existingCodes);
            return 'hasbatches';
        }
        // this unit has been removed from the previous list..
        $removed = Subject::RemoveUnitsFromSubject($excludedUnits, $subject->id);
        // unit master array;;
        $deliveryModesArray = str_split($inputData->delivery_mode);
        $unitData = [
            'college_id' => $college_id,
            'subject_id' => $subject->id,
            'course_id' => $courseId,
            'unit_code' => null,
            'vet_unit_code' => null,
            'unit_name' => null,
            'description' => $inputData->description ?? '',
            'unit_type' => $inputData->unit_type ?? null,
            'field_education' => $inputData->field_of_education_id ?? 0,
            'delivery_mode' => $inputData->delivery_mode,
            'internal' => $deliveryModesArray[0] ?? 'N',
            'external' => $deliveryModesArray[1] ?? 'N',
            'workplace_based_delivery' => $deliveryModesArray[2] ?? 'N',
            'nominal_hours' => $inputData->nominal_hours ?? 0,
            'tution_fees' => $inputData->subject_fee ?? null,
            'module_unit_flag' => $inputData->module_unit_flag ?? null,
            'vet_flag' => $inputData->vet_flag ?? null,
            'work_placement' => $inputData->work_placement ?? null,
            'AVETMISS_Report' => $inputData->AVETMISS_Report ?? null,
            'status' => $inputData->status ?? null,
            'created_by' => $user_id,
            'updated_by' => $user_id,
        ];
        $added = Subject::AddUnitsToSubject($addedUnits, $unitData, false); // false because no new unit will be created from this flow

        return ($added > 0) ? true : 'nounitsadded';
    }

    public function MapCourseSubject($subjectData = [])
    {
        if (empty($subjectData)) {
            return false;
        }
        $MappedData = CourseSubject::with('subject')->where(['college_id' => $subjectData['college_id'], 'course_id' => $subjectData['course_id'], 'subject_id' => $subjectData['subject_id']])->first();
        if (! $MappedData) {
            Arr::forget($subjectData, 'updated_by');
            $subjectData['display_order'] = 999;
            $saved = CourseSubject::create($subjectData);
        } else {
            // check if the course subject can be mapped
            $checkFields = [
                'course_stage',
                'subject_type',
                'domestic_subject_fee',
                'unit_type',
                'delivery_mode',
                'EFTSL_study_load',
                'contact_hours',
                'is_active',
                'inc_in_certificate',
            ];
            $changesDetected = false;
            foreach ($checkFields as $field) {
                // Check if the field value is different between the two objects
                if ($MappedData->$field !== null && $subjectData[$field] !== $MappedData->$field) {
                    // echo $field." ==>> ".$MappedData->$field." !== ".$subjectData[$field]."<br />";
                    $changesDetected = true;
                    break;
                }
            }
            if ($changesDetected && $this->hasBatches() > 0) {
                return 'hasbatches';
            }
            Arr::forget($subjectData, 'created_by');
            $saved = $MappedData->update($subjectData);
        }

        return ($saved) ? true : false;
    }

    /*
    Function to check if the unit can be removed
    */
    private static function CheckIfSubjectCanBeDeleted(Subject $subject)
    {
        // for now allow to delete
        return true;
    }

    /*
    Function to check if the unit can be removed
    */
    private static function CheckIfUnitCanBeDeleted(UnitModule $unit, $hardRemove = false)
    {
        // for now allow to delete
        if ($hardRemove == true && $unit->subject_id > 0) {
            return 'Units assigned to subjects cannot be deleted.';
        }

        return true;
    }

    /*
    Function to check if the unit can be changed
    this method checks when the units are moved/switched in different subjects within a course
    */
    private static function CheckIfUnitCanBeChanged(UnitModule $unit, $newValue = [])
    {
        // for now allow to change everything
        return true;
    }

    public static function RemoveUnitsFromSubject($units = [], $subject = 0, $delete = false)
    {
        if (empty($units)) {
            return;
        }
        $unitIds = Arr::pluck($units, 'id');
        $unitsCollection = UnitModule::whereIn('id', $unitIds)->get();
        $unitsCollection->each(function ($unit) {
            if ($unit->canBeDeleted()) { // check if unit can be deleted
                // $item->update(['your_column' => 'new_value']);
                $unit->subject_id = null;
                $unit->save();
            }
        });
    }

    /*
    Assign the units to the mapped subject
    UnitData must be mapped to CourseSubject before this process
    */
    public static function AddUnitsToSubject($addedUnits = [], $unitData = [], $insertNew = true)
    {
        $subjectId = $unitData['subject_id'] ?? null;
        if (empty($unitData) || empty($subjectId)) {
            return false;
        }
        $createdBy = $unitData['created_by'];
        // remove created by field to stop updating every record's created_by field
        Arr::forget($unitData, 'created_by');
        $unitsCnt = 0;
        foreach ($addedUnits as $unit) {
            $unitId = $unit['unit_details']['id'] ?? false;
            $unitCode = $unit['Code'] ?? '';
            $unitType = ($unit['IsEssential']) ? 'Core' : 'Elective';
            $unitName = $unit['Title'] ?? '';
            if (empty($unitCode) && empty($unitId)) {
                continue;
            }
            if ($unitId > 0) {
                // check by id
                $filterParams = ['college_id' => $unitData['college_id'], 'id' => $unitId];
            } else {
                // check by code
                $filterParams = ['college_id' => $unitData['college_id'], 'course_id' => $unitData['course_id'], 'unit_code' => $unitData['unit_code']];
            }
            /* set selected units individual data to insert/update */
            $existingUnit = UnitModule::where($filterParams)->first();
            if (! $existingUnit && $insertNew == true) {
                $unitData['unit_code'] = $unitCode;
                $unitData['unit_type'] = $unitType;
                $unitData['unit_name'] = $unitName;
                // remove updated by field to stop adding updated_by field in newly created record
                Arr::forget($unitData, 'updated_by');
                $existingUnit = new UnitModule($unitData);
                $existingUnit->created_by = $createdBy;
                $saved = $existingUnit->save();
                $unitsCnt += ($saved) ? 1 : 0;

                continue;
            }
            // $allowed = Subject::CheckIfUnitCanBeChanged($existingUnit, $unitData);
            if (! $existingUnit->canBeDeleted()) {
                $unitsCnt++; // the units will remain same

                // this subject can't be changed
                continue;
            }
            $unitDataChunked = Arr::only($unitData, ['subject_id', 'updated_by']);
            $updated = $existingUnit->update($unitDataChunked);
            $unitsCnt += ($updated) ? 1 : 0;
        }

        return $unitsCnt;
    }

    public static function DeleteSubjectForCourse(Subject $subject, Courses $course)
    {
        // get all the course subjects for the course
        $candelete = $subject->canBeDeleted(true);
        if ($candelete === true) {
            $deleted = CourseSubject::where(['course_id' => $course->id, 'subject_id' => $subject->id])->delete();
            if ($deleted) {
                // remove the subjectid from units
                UnitModule::where(function ($query) use ($course) {
                    $query->whereNull('course_id')
                        ->orWhere('course_id', $course->id);
                })
                    ->where('subject_id', $subject->id)
                    ->update(['subject_id' => null]);

                // course can be deleted from table if not used for other courses
                return true;
            }
        }

        return $candelete;
    }

    public static function DeleteUnitForCourse(UnitModule $unit, Courses $course)
    {
        $candelete = $unit->canBeDeleted();
        if ($candelete === true) {
            $deleted = $unit->delete();
            if ($deleted) {
                return true;
            }
        }

        return $candelete;
    }

    public function getSubjectlistCollegeIdWise($subjectId)
    {
        $data = Subject::where('id', $subjectId)->get();

        return $data;
    }

    public function getSubject($collegeId)
    {
        $arrSubject = Subject::where('college_id', '=', $collegeId)
            ->select('subject_name', 'subject_code', 'id')
            ->get()
            ->toArray();

        $arrSubjectList[''] = '- - Select Subject - -';
        foreach ($arrSubject as $row) {
            $arrSubjectList[$row['id']] = $row['subject_code'].' : '.Str::ascii($row['subject_name']);
        }

        return $arrSubjectList;
    }
}
