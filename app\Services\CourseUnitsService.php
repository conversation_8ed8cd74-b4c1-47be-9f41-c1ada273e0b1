<?php

namespace App\Services;

use App\Classes\CoursesApiController;
use App\Classes\SiteConstants;
use App\DTO\Courses\ApiResponseCourseDTO;
use App\DTO\Courses\ApiResponseUnitDTO;
use App\DTO\Courses\CourseUnitDTO;
use App\Http\Resources\SubjectUnitsResource;
use App\Model\v2\AgreedNominalHours;
use App\Model\v2\Courses;
use App\Model\v2\CourseSubjects;
use App\Model\v2\CourseTemplate;
use App\Model\v2\Subject;
use App\Model\v2\SubjectUnits;
use App\Model\v2\Units;
use App\Process\Course\AddUnitToCourseProcess;
use App\ProcessTask\Course\FindOrCreateTemplate;
use App\ProcessTask\Course\Payloads\AddUnitToCoursePayload;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use phpDocumentor\Reflection\Types\Integer;

class CourseUnitsService
{
    private $coursesApi = null;

    public function __construct()
    {
        $this->coursesApi = new CoursesApiController;
    }

    /*
    function to load units for a course
    this function will load all the units that can be added to a course
    */
    public function loadUnits(?Courses $course = null, $courseCode = '', ?CourseTemplate $templateData = null): ?ApiResponseCourseDTO
    {
        $college_id = Auth::user()->college_id;

        // if course is null; the course is not saved
        if (! $course && empty($courseCode)) {
            return null;
        }

        $vetCourseType = config('constants.vet_course_type_id');
        $shortCourseType = config('constants.short_course_type_id');
        $courseId = $course->id ?? null;
        $isVetCourse = $loadFromApi = false;
        // if called this function and if course is empty;
        // the course is trying to add units that means the course type is vet and
        // the request is to get units from the tga
        if (empty($course) || $course->course_type_id == $vetCourseType) {
            $isVetCourse = true;
            $loadFromApi = true;
            $courseCode = empty(! $course) ? $course->course_code : $courseCode;
        }
        $templateUnits = $templateData->templateunits ?? null;
        $templateSubjects = $templateData->templatesubjects ?? null;

        $templateUnitIds = ($templateUnits) ? $templateUnits->pluck('unit_id')->toArray() : [];

        $resArray = [];
        $unitsData = collect([]);
        // Get the unit details from the api (training.gov.au)
        $courseDetail = ($loadFromApi) ? $this->coursesApi->loadCourseUnits($courseCode, true, $unitsData) : ApiResponseCourseDTO::LazyFromArray([]);
        if (empty($courseDetail->Code)) {
            // set basic info from course
            $courseDetail->Code = $course->course_code ?? null;
            $courseDetail->Title = $course->course_name ?? null;
            $courseDetail->national_code = $course->national_code ?? null;
            $courseDetail->course_code = $course->course_code ?? null;
            $courseDetail->course_name = $course->course_name ?? null;
            $courseDetail->course_type_id = $course->course_type_id ?? null;
            $courseDetail->is_vet_synced = false;
            $courseDetail->ComponentType = $course->ComponentType ?? null;
            $courseDetail->CurrencyStatus = $course->CurrencyStatus ?? null;
            $courseDetail->ANZSCO_code = $course->ANZSCO_code ?? null;
            $courseDetail->field_of_education_id = $course->field_of_education_id ?? null;
            $courseDetail->level_of_education_id = $course->level_of_education_id ?? null;
            $courseDetail->course_recognition_id = $course->course_recognition_id ?? null;
            $courseDetail->total_nominal_hours = $course->total_nominal_hours ?? null;
            $courseDetail->is_superseded = $course->is_superseded ?? null;
            $courseDetail->superseded_date = $course->superseded_date ?? null;
            $courseDetail->superseded_course = $course->superseded_course ?? null;
            $courseDetail->units = null;
            $courseDetail->CourseTypeId = null;
            $courseDetail->CourseType = null;
            $courseDetail->subjects = null;
        }
        // if course is empty and the course detail is not found even in tga site, then return null
        if (empty($courseDetail->Code) && empty($courseId)) {
            return null;
        }

        $unitsFromApi = $courseDetail->units ?? null;

        if ($courseId) {
            $unitsData = SubjectUnits::with([
                'subject',
                'masterdata',
            ])
                ->where('course_id', $courseId)->get();
        } else {
            if (empty($courseDetail->Code)) {
                $courseDetail = ApiResponseCourseDTO::LazyFromArray([
                    'Code' => $course->course_code ?? null,
                    'Title' => $course->course_name ?? null,
                ]);
            } else {
                $courseType = $courseDetail->ComponentType == 'SkillSet' ? $shortCourseType : $vetCourseType;
                $courseDetail->course_type_id = $courseType ?? 0;
            }
            $course = new Courses($courseDetail->toArray());
        }
        // if ($courseDetail && $courseDetail["Units"]) {
        //     Arr::forget($courseDetail, 'Units');
        // }
        $courseUnitsDetailArray = $availableUnits = [];
        $foundUnits = [];

        if ($unitsFromApi && is_array($unitsFromApi)) {
            foreach ($unitsFromApi as $unit) {
                // dump($unit);
                // dump($unitsData);
                $preparedUnit = $this->prepareUnits($unit, $unitsData, 'api');
                if ($preparedUnit->ID) {
                    $foundUnits[] = $preparedUnit->ID;
                }
                if (in_array($preparedUnit->ID, $templateUnitIds)) {
                    $preparedUnit->templateData = $templateData->id ?? 'master';
                    $templateUnit = $templateData->templateunits->where('unit_id', $preparedUnit->ID)->first();
                    $templateUnitId = $templateUnit->unit_id ?? null;
                    $templateUnitType = $templateUnit->unit_type ?? 'Elective';
                    $templateIsEssential = $templateUnitType == 'Core';
                    if ($templateUnitId) {
                        $preparedUnit->Type = $templateUnitType;
                        $preparedUnit->IsEssential = $templateIsEssential;
                        $preparedUnit->unit_details->unit_type = $templateUnitType;
                    }
                }
                /*
                if the unit is not in a template then we will treat as not added even if added to master template
                If no template is selected; the template if added will have master value hence will be treate as added if added
                */
                // dd($preparedUnit);
                if ($preparedUnit->Added == true && empty($preparedUnit->templateData)) {
                    $preparedUnit->Added = false;
                    $preparedUnit->isInOtherTemplate = true;
                }
                $courseUnitsDetailArray[] = $preparedUnit;
            }
        }

        // remaining units found in database
        // this will give any other units that were custom added and not available in the tga site
        $remainingUnits = $unitsData->whereNotIn('id', $foundUnits);

        if ($remainingUnits) {
            foreach ($remainingUnits as $unit) {
                // $masterData = $unit->masterdata ?? [];
                // $unitSource = $masterData["unit_source"] ?? 'custom';
                $unitSource = 'custom';
                $preparedUnit = $this->prepareUnits($unit, null, $unitSource);
                if (in_array($preparedUnit->ID, $templateUnitIds)) {
                    $preparedUnit->templateData = $templateData->id ?? 'master';
                    $templateUnit = $templateData->templateunits->where('unit_id', $preparedUnit->ID)->first();
                    $templateUnitId = $templateUnit->unit_id ?? null;
                    $templateUnitType = $templateUnit->unit_type ?? 'Elective';
                    $templateIsEssential = $templateUnitType == 'Core';
                    if ($templateUnitId) {
                        $preparedUnit->Type = $templateUnitType;
                        $preparedUnit->IsEssential = $templateIsEssential;
                        $preparedUnit->unit_details->unit_type = $templateUnitType;
                    }
                }
                /*
                if the unit is not in a template then we will treat as not added even if added to master template
                If no template is selected; the template if added will have master value hence will be treate as added if added
                */
                if ($preparedUnit->Added == true && empty($preparedUnit->templateData)) {
                    $preparedUnit->Added = false;
                    $preparedUnit->isInOtherTemplate = true;
                }

                $courseUnitsDetailArray[] = $preparedUnit;
            }
        }
        usort($courseUnitsDetailArray, function ($a, $b) {
            if ($a->Added !== $b->Added) {
                return $b->Added <=> $a->Added;
            }
            // Compare IsEssential (true should come before false)
            if ($a->IsEssential !== $b->IsEssential) {
                return $b->IsEssential <=> $a->IsEssential;
            }
            // Compare IsEssential (true should come before false)
            $displayOrderA = $a->display_order ?? 0;
            $displayOrderB = $b->display_order ?? 0;
            if ($displayOrderA !== $displayOrderB) {
                return $displayOrderB <=> $displayOrderA;
            }

            // Compare Title (alphabetical order)
            return $a->Title <=> $b->Title;
        });
        $unitCodesFound = array_map(function ($item) {
            return $item->Code; // or $item->unit_code if that's the field you want
        }, $courseUnitsDetailArray);
        $nominalHours = AgreedNominalHours::where('status', 1)->whereIn('code', $unitCodesFound)->pluck('agreed_hours', 'code')->toArray();
        /* update the standard nominal hours of the units */
        foreach ($courseUnitsDetailArray as $unit) {
            $hrs = $nominalHours[$unit->Code] ?? null;
            $savedHrs = $unit->unit_details->nominal_hours ?? null;
            if (! $savedHrs && $hrs) {
                $unit->unit_details->nominal_hours = $hrs;
                $savedHrs = $hrs;
            }
            $isDifferent = false;
            if ($hrs && $hrs != $savedHrs) {
                $isDifferent = true;
            }
            $unit->nominal_hours = $hrs;
            $unit->nominal_hours_different = $isDifferent;
        }
        $courseDetail->units = $courseUnitsDetailArray;

        // $courseDetail['availableUnits'] = $availableUnits;
        return $courseDetail;
    }

    /*
    $unit unit info from api/custom this is
    $unitsData => List of units added to the course in rto_subject_units
    $source => source of the unit api/custom
    */
    public function prepareUnits(ApiResponseUnitDTO|SubjectUnits $unit, $unitsData, $source = 'custom')
    {
        if ($source == 'custom') {
            /*
            this is custom unit and it will be added in db so we need not to format much
            just prepare the required array structrue and return
            */
            $unitData = $unit;
            $unitSubject = $unitData->subject ?? [];
            $unitStatus = $unitData->status ?? null;
            $unit->unit_source = $source;
            $res = CourseUnitDTO::LazyFromArray([
                'ID' => $unit->id ?? null,
                'Code' => $unit->unit_code ?? '',
                'Title' => $unit->unit_name ?? '',
                'unit_code' => $unit->unit_code,
                'unit_name' => $unit->unit_name,
                'IsEssential' => ($unit->unit_type === SiteConstants::CORETEXT) ? true : false,
                'Type' => $unit->unit_type ?? SiteConstants::ELECTIVETEXT,
                'Added' => $unit->status === 1,
                'Source' => $source,
                'subject_code' => $unitSubject->subject_code ?? '',
                'subject_name' => $unitSubject->subject_name ?? '',
                'subject_display_order' => $unitSubject->display_order ?? 0,
                'display_order' => $unitData->display_order ?? 0,
                'primary_id' => $unitSubject->id ?? null,
                'map_id' => $unitSubject->synced_subject ?? null,
                'unit_details' => new SubjectUnitsResource($unit),
            ]);

            return $res;
        }
        /*
        Units that pass through here are the units sent by API we need to adjust it according to our saved data if available
        and prepare the required array structrue to return
        */
        // get saved record if any
        $unitData = ($unitsData) ? $unitsData->where('unit_code', $unit->Code)->first() : null;
        // subject of that unit
        $unitSubject = $unitData->subject ?? [];
        // status of that unit 1 is added and 0 is not added
        $unitStatus = $unitData->status ?? null;
        $isAdded = $unitStatus == 1 ? true : false;
        // create basic array with basic unit info $unit holds the data sent from api
        $unitValues = [
            'unit_code' => $unit->Code ?? '',
            'vet_unit_code' => $unit->Code ?? '',
            'unit_name' => $unit->Title ?? '',
            'description' => $unit->Description ?? '',
            'unit_type' => $unit->Type,
        ];

        // get classification data according to the data sent from api
        $classifications = $unit->Classifications['Classification'] ?? [];
        for ($i = 0; $i < count($classifications); $i++) {
            $clsfData = $classifications[$i] ?? [];
            $schemeCode = $clsfData['SchemeCode'] ?? null;
            $valueCode = $clsfData['ValueCode'] ?? null;
            if ($schemeCode == 03) {
                $unitValues['field_education'] = $valueCode;
            } elseif ($schemeCode == 06) {
                $unitValues['module_unit_flag'] = $valueCode;
            }
        }
        $unitValues['nominal_hours'] = null;
        $unitValues['unit_source'] = 'api';
        $unitValues['unit_scope'] = 'public';
        $unitValues['status'] = $isAdded ? 1 : 0;
        $unitActiveFrom = $unit->CurrencyPeriods['NrtCurrencyPeriod']['StartDate'] ?? null;
        $unitActiveTo = $unit->CurrencyPeriods['NrtCurrencyPeriod']['EndDate'] ?? null;
        $isSuperseded = ! (($unit->CurrencyStatus ?? '') == 'Current');
        $supersededBy = $unit->ReverseMappingInformation['Mapping'] ?? null;
        $unitValues['active_from'] = $unitActiveFrom;
        $unitValues['is_superseded'] = $isSuperseded;
        $unitValues['superseded_date'] = $unitActiveTo;
        $unitValues['superseded_by'] = $supersededBy;

        if (empty($unitData)) {
            // unit was not saved in our table so this is the fresh unit retrived from api
            // create a model with the api data
            $unitData = new SubjectUnits($unitValues);
        } else {
            // mark the id as found
            // this will map and pair the unit that is saved in our end and unit retrived from the api
            $foundUnits = $unitData->id;
        }
        $unitData->unit_source = 'api';
        /*
        the master data is the raw info in our units table that was first received in and as from api
        master data can be a custom created unit as well
        masterdata table (rto_units) will have only unique units
        */
        if (! $unitData->masterdata) {
            /*
            No master data found so create one with the units info retrived from api
            */
            $unitValues['status'] = 1;
            $masterData = new Units($unitValues);
            // set the relation of master data with unit data
            $unitData->setRelation('masterdata', $masterData);
        }

        return CourseUnitDTO::LazyFromArray([
            'ID' => $unitData->id ?? null,
            'Code' => $unit->Code ?? '',
            'Title' => $unit->Title ?? '',
            'unit_code' => $unit->Code,
            'unit_name' => $unit->Title,
            'IsEssential' => $unit->IsEssential ?? '',
            'Type' => $unit->IsEssential ? SiteConstants::CORETEXT : SiteConstants::ELECTIVETEXT,
            'Added' => $isAdded,
            'Source' => $source,
            'subject_code' => $unitSubject->subject_code ?? '',
            'subject_name' => $unitSubject->subject_name ?? '',
            'subject_display_order' => $unitSubject->display_order ?? 0,
            'display_order' => $unitData->display_order ?? 0,
            'primary_id' => $unitSubject->id ?? null,
            'map_id' => $unitSubject->synced_subject ?? null,
            'unit_details' => new SubjectUnitsResource($unitData),
            'StartDate' => $unitActiveFrom,
            'EndDate' => $unitActiveTo,
            'Superseded' => $isSuperseded,
            'SupersededBy' => $supersededBy,
        ]);
    }

    public function saveUnits($units, Courses $course, CourseTemplate|Integer|null $templateData = null)
    {
        /* check if the units have rooms to be added to the template */
        /*
        first get the current units in the course
        Analyze the units to be added and/or removed and find the final count of units the course will have after the units are added removed
        */
        $payload = new AddUnitToCoursePayload(
            course: $course,
            template: $templateData
        );
        $templateHandler = new FindOrCreateTemplate;
        $payload = $templateHandler->handle($payload);
        /*
        If the coures was recently created then no need to check the template size
        the template will be created by default with the number of units selected
        */
        $coreUnitsCodes = array_column(array_filter($units, function ($unit) {
            return $unit['Type'] == SiteConstants::CORETEXT && $unit['Added'] == true;
        }), 'Code');
        $electiveUnitsCodes = array_column(array_filter($units, function ($unit) {
            return $unit['Type'] == SiteConstants::ELECTIVETEXT && $unit['Added'] == true;
        }), 'Code');

        if ($course->wasRecentlyCreated) {
            /* update the course template counts */
            $payload->template->no_of_core_subject = count($coreUnitsCodes) ?? 0;
            $payload->template->no_of_elective_subject = count($electiveUnitsCodes) ?? 0;
            $payload->template->save();
            $payload->course->core_units_number = count($coreUnitsCodes) ?? 0;
            $payload->course->elective_units_number = count($electiveUnitsCodes) ?? 0;
            $payload->course->save();
        } else {
            /* if not check for the units in the template and check if the selected units can accomodate the template */
            $maxCoreUnits = $payload->template->no_of_core_subject ?? 0;
            $maxElectiveUnits = $payload->template->no_of_elective_subject ?? 0;
            $coreUnitsCount = count($coreUnitsCodes) ?? 0;
            $electiveUnitsCount = count($electiveUnitsCodes) ?? 0;
            if ($coreUnitsCount > $maxCoreUnits || $electiveUnitsCount > $maxElectiveUnits) {
                if ($coreUnitsCount > $maxCoreUnits && $electiveUnitsCount > $maxElectiveUnits) {
                    $message = 'The number of core and elective units selected exceeds the maximum number of units allowed for the course or course major';
                } elseif ($electiveUnitsCount > $maxElectiveUnits) {
                    $message = 'The number of elective units selected exceeds the maximum number of units allowed for the course or course major';
                } else {
                    $message = 'The number of core units selected exceeds the maximum number of units allowed for the course or course major';
                }
                throw new \Exception($message);
            }
        }
        $unitsSaved = $unitsFailed = [];

        foreach ($units as $ind => $val) {
            DB::beginTransaction();
            try {
                $unitDTO = CourseUnitDTO::LazyFromArray($val);
                $payload->unitToSave = $unitDTO;
                $payload->masterUnit = null;
                $payload->subject = null;
                $payload->subjectUnit = null;
                $process = new AddUnitToCourseProcess;
                $result = $process->run($payload);
                $unitsSaved[] = $result->subjectUnit;
                DB::commit();
            } catch (\Exception $e) {
                $val['failed_message'] = $e->getMessage();
                $unitsFailed[] = $val;
                DB::rollBack();
            }
        }
        // $results = ['saved' => $unitsSaved, 'failed' => $unitsFailed];

        // dd($results);

        return ['saved' => $unitsSaved, 'failed' => $unitsFailed];
    }

    public function saveCustomUnit($unitData, CourseTemplate $template)
    {
        $courseId = $unitData['course_id'] ?? 0;
        $course = Courses::find($courseId);

        if (! $course) {
            $errorMessage = config_locale('messages.courses.nocourse', 'No Course');
            throw new \Exception($errorMessage);
        }

        $unitsCollection = collect([collect($unitData)]);
        $soloSubject = $unitData['solo_subject'] ?? false;
        Arr::forget($unitData, 'solo_subject');

        $subject = null;

        if ($soloSubject == false && $unitData['course_subject_id']) {
            $subject = CourseSubjects::with(['subject'])->find($unitData['course_subject_id']);
            // $subject = $courseSubject->subject ?? null;
            if (! $subject) {
                $errorMessage = config_locale('messages.courses.subjectnotfound', 'No Subject');
                throw new \Exception($errorMessage);
            }
            $unitData['subject_code'] = $subject->subject_code;
            $unitData['subject_name'] = $subject->subject_name;
            $unitData['subject_display_order'] = $subject->display_order;
        }

        // $masterUnit = $this->transferUnits($unitsCollection)->first();
        $masterUnit = SyncUnitsSetupService::SaveUnitsToMasterUnitsCollectionTable($unitsCollection)->first();

        if ($template) {
            $template->setUnitsBufferFromTemplate($unitData);
        }

        $unitData = CourseUnitDTO::LazyFromArray([
            'ID' => null,
            'Code' => $unitData['unit_code'],
            'Title' => $unitData['unit_name'],
            'unit_code' => $unitData['unit_code'],
            'unit_name' => $unitData['unit_name'],
            'IsEssential' => $unitData['unit_type'] === 'Core',
            'Type' => $unitData['unit_type'],
            'Added' => true,
            'Source' => 'custom',
            'subject_type' => $unitData['Type'] ?? 'Elective',
            'subject_code' => ($soloSubject) ? $unitData['unit_code'] : '',
            'subject_name' => ($soloSubject) ? $unitData['unit_name'] : '',
            'subject_display_order' => ($soloSubject) ? $unitData['display_order'] ?? null : null,
            'display_order' => $unitData['display_order'] ?? 999,
            'primary_id' => null,
            'map_id' => null,
            'unit_details' => $unitData,
            'Selected' => true,
        ]);

        $unit = $course->addUnitToCourse($unitData, $masterUnit, $subject, $template);

        return $unit;
    }

    public function updateUnit($unitData = [], ?CourseTemplate $template = null)
    {
        $defaultErrorMsg = 'Could not save unit! Unknown error occurred';
        $templateId = $template->id ?? null;
        $courseId = $unitData['course_id'] ?? 0;

        $course = Courses::find($courseId);
        if (! $course) {
            $errorMessage = config_locale('messages.courses.nocourse', $defaultErrorMsg);
            throw new \Exception($errorMessage);
        }
        // find the current unit from the table
        $unitRecord = SubjectUnits::find($unitData['id']);
        $currentSubject = $unitRecord->course_subject_id ?? 0;
        if (! $unitRecord) {
            $errorMessage = config_locale('messages.courses.unitnotfound', $defaultErrorMsg);
            throw new \Exception($errorMessage);
            // returrn the erro message
        }

        // check if unit can be updated
        // if unit has enrolled students; the unit type can not be changed
        // according to the course template check if there is room for the unit type selected
        $editable = $unitRecord->canBeUpdated($unitData);
        if ($editable !== true) {
            $errorMessage = config_locale('messages.courses.cannotupdateunit', $defaultErrorMsg);
            throw new \Exception($errorMessage);
        }

        $newSubject = $unitData['course_subject_id'] ?? 0;

        // check if destination subject is editable if not editable then the unit should not be able to be transferred
        if ($newSubject && $currentSubject != $newSubject) {
            // if the unit can be added to the selected subject
            $editable = CourseSubjects::find($newSubject)->canBeUpdated();
            if ($editable !== true) {
                $errorMessage = config_locale('messages.courses.cannotupdatesubjectunit', $defaultErrorMsg);
                throw new \Exception($errorMessage);
            }
        }

        // if ($template->is_master_template) {
        //     // if the unit type is chagned in master template
        //     $unitChanged = $unitRecord->unit_type != $unitData['unit_type'];
        // } else {
        // if unit type is changed only for the template, we need not to update the unit type of master unit record
        // only update the record of the template structure
        $templateUnit = $template->templateunits->where('unit_id', $unitRecord->id)->first();
        $templateUnitType = $templateUnit->unit_type ?? null;
        $unitChanged = $templateUnitType != $unitData['unit_type'];
        $template->setUnitsBufferFromTemplate($unitChanged ? $unitData : null, 'id');
        $canHold = $template->hasSpaceForUnitId($unitData['id'], $unitData['unit_type']);
        if ($canHold) {
            $templateUnit->unit_type = $unitData['unit_type'];
            $templateUnit->save();
        }
        // $template = null;
        // dd($unitData);
        // unset($unitData["unit_type"]);
        // }

        $unit = $unitRecord->updateCourseUnit($unitData, $template);

        return $unit;
    }
}
