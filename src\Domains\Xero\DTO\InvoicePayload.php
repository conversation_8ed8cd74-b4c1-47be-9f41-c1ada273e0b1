<?php

namespace Domains\Xero\DTO;

use App\Model\v2\Agent;
use App\Model\v2\StudentAgentCommission;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentMiscellaneousPayment;
use App\Model\v2\StudentScholarship;
use App\Model\v2\StudentServicePayment;
use DateTime;
use Domains\Xero\Entities\Invoice;
use Domains\Xero\Models\XeroConfig;
use Support\Contracts\ArrayableEntity;
use Support\Contracts\CanValidate;
use Support\DTO\BasePayload;
use Support\Traits\ArrayToProps;

class InvoicePayload extends BasePayload implements ArrayableEntity, CanValidate
{
    use ArrayToProps;

    public $id;

    public $type;

    public $invoiceNumber;

    public $quantity;

    public $accountCode;

    public $taxCode;

    public $discountRate;

    public $amount;

    public $unitdp;

    public $date;

    public $dueDate;

    public $description;

    public $lineAmountTypes;

    public $status = Invoice::STATUS_DRAFT;

    // public $miscellaneousPayments;
    public $materialFee;

    public $enrollmentFee;

    public $ohscFee;

    public $ohscProvider;

    public $tracking;

    public $rules = [
        'invoiceNumber' => 'required_unless:type,'.Invoice::TYPE_ACCPAY,
        // 'date' => 'required_unless:type,' . Invoice::TYPE_ACCPAY,
        'dueDate' => 'required_unless:status,'.Invoice::STATUS_DRAFT,
        // 'dueDate' => 'required',
        'accountCode' => 'required',
    ];

    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    public function setDescription($description)
    {
        $this->description = $description;

        return $this;
    }

    public static function FromStudentInitialPaymentDetail(StudentInitialPaymentDetails $initialPaymentDetail, $extra = []): InvoicePayload
    {
        $installmentDescription = sprintf(
            '%s for "%s: %s"',
            $initialPaymentDetail->isInitial() ? 'Pre-Payment' : 'Tuition fee',
            @$initialPaymentDetail->course->course_code ?? '',
            @$initialPaymentDetail->course->course_name ?? ''
        );
        $props = array_merge([
            // 'invoiceNumber' => 'GAL-' . $initialPaymentDetail->invoice_number . '-' . $initialPaymentDetail->id,
            'invoiceNumber' => self::generateFormattedInvoiceNumber($initialPaymentDetail, 'schedule'),
            'quantity' => 1,
            /* TODO: need to pass 2nd parameter campus id for this payment detail student */
            'accountCode' => XeroConfig::AccountCode(XeroConfig::ACCOUNT_COURSE, @$initialPaymentDetail->student->collegeCampus->id),
            'taxCode' => XeroConfig::TaxCode(XeroConfig::ACCOUNT_COURSE, @$initialPaymentDetail->student->collegeCampus->id),
            // 'discountRate' => 0.0,
            // 'status' => Invoice::STATUS_AUTHORISED,
            'tracking' => [self::GetTrackingForAgent($initialPaymentDetail->agent)],
            'lineAmountTypes' => 'Exclusive',
            // 'description' => ($initialPaymentDetail->isInitial() ? 'PP ' : '') . @$initialPaymentDetail->student->full_name . ' (' . @$initialPaymentDetail->course->course_code . ')',
            'description' => $installmentDescription,
            'unitdp' => 4,
            'amount' => @$initialPaymentDetail->upfront_fee_to_pay - (@$initialPaymentDetail->invoice_credit ?? 0), // TODO::GNG-4773
            'date' => (new DateTime(@$initialPaymentDetail->invoiced_start_date))->format('Y-m-d'),
            'dueDate' => $initialPaymentDetail->due_date ? (new DateTime($initialPaymentDetail->due_date))->format('Y-m-d') : null,
        ], $extra);

        // if ($initialPaymentDetail->isInitial()) {
        //     $props['miscellaneousPayments'] = $initialPaymentDetail->miscellaneousPayments()->select(['payment_type', 'amount', 'oshc_start_date', 'oshc_finish_date'])->get();
        //     // foreach($miscellaneousPayments as $mPayment){

        //     // }
        // }
        return InvoicePayload::LazyFromArray($props);
    }

    public static function FromStudentAgentCommission(StudentAgentCommission $studentAgentCommission, $extra = [])
    {

        if ($studentAgentCommission->GST == 'GST') {
            $gstStatus = XeroConfig::AGENT_COMMISSION_GST;
        } else {
            $gstStatus = XeroConfig::AGENT_COMMISSION_NO_GST;
        }

        return InvoicePayload::LazyFromArray([
            // 'invoiceNumber' => @$studentAgentCommission->invoice_number,
            'invoiceNumber' => self::generateFormattedInvoiceNumber($studentAgentCommission, 'agent_commission'),
            'quantity' => 1,
            'type' => Invoice::TYPE_ACCPAY,
            /* TODO: if this commission has no gst should use ::AGENT_COMMISSION_NO_GST */
            'accountCode' => XeroConfig::AccountCode($gstStatus),
            /* TODO: if this commission has no gst should use ::AGENT_COMMISSION_NO_GST */
            'taxCode' => XeroConfig::TaxCode($gstStatus),
            // 'description' => $student->full_name . ' (' . $course->course_code . ')',
            'lineAmountTypes' => 'Exclusive',
            'discountRate' => 0, // TODO add discount if needed
            'status' => Invoice::STATUS_DRAFT,
            'unitdp' => 4,
            'amount' => (@$studentAgentCommission->commission_payable + @$studentAgentCommission->gst_amount),
            /* invoice date will be the date when the commission bill is created which is the date payment is received for schedule */
            'date' => now()->format('Y-m-d'),
            // 'date' => $studentAgentCommission->paid_date ? (new DateTime(@$studentAgentCommission->paid_date))->format("Y-m-d") : null,
            'dueDate' => $studentAgentCommission->due_date ? (new DateTime($studentAgentCommission->due_date))->format('Y-m-d') : null,  // TODO::GNG-3144
        ]);
    }

    public static function FromMiscellaneousPayment(StudentMiscellaneousPayment $miscellaneousPayments, $extra = [])
    {
        $miscellaneousDescription = sprintf(
            'Miscellaneous payment (%s) for "%s: %s"',
            @$miscellaneousPayments->payment_type,
            @$miscellaneousPayments->course->course_code,
            @$miscellaneousPayments->course->course_name
        );

        return InvoicePayload::LazyFromArray(array_merge([
            // 'invoiceNumber' => 'GAL-MISC-' . $miscellaneousPayments->invoice_number . '-' . $miscellaneousPayments->id,
            'invoiceNumber' => self::generateFormattedInvoiceNumber($miscellaneousPayments, 'miscellaneous'),
            'quantity' => 1,
            /* TODO: need to pass 2nd parameter in "AccountCodeForMiscellaneousPayment" campus id for student who made this  miscellaneous payment */
            'accountCode' => XeroConfig::AccountCodeForMiscellaneousPayment($miscellaneousPayments->payment_type, @$miscellaneousPayments->student->collegeCampus->id),
            'discountRate' => 0.0,
            'status' => Invoice::STATUS_DRAFT,
            'lineAmountTypes' => 'Exclusive',
            // 'description' => @$miscellaneousPayments->payment_type . ' (' . @$miscellaneousPayments->course->course_code . ')',
            'description' => $miscellaneousDescription,
            'unitdp' => 4,
            'amount' => @$miscellaneousPayments->amount,
            'date' => (new DateTime(@$miscellaneousPayments->invoiced_start_date))->format('Y-m-d'),
            'dueDate' => $miscellaneousPayments->due_date ? (new DateTime($miscellaneousPayments->due_date))->format('Y-m-d') : null,
            // 'dueDate' => (new DateTime(@$initialPaymentDetail->due_date))->format("Y-m-d")
        ], $extra));
    }

    public static function FromStudentService(StudentServicePayment $servicePayment, $extra = [])
    {
        // $description = @$servicePayment->additionalServiceDetail->short_description;
        $serviceDescription = sprintf(
            'Service payment (%s) for "%s: %s"',
            @$servicePayment->additionalServiceDetail->short_description,
            @$servicePayment->course->course_code,
            @$servicePayment->course->course_name
        );

        return InvoicePayload::LazyFromArray(array_merge([
            // 'invoiceNumber' => 'GAL-SRV-' . $servicePayment->invoice_number . '-' . $servicePayment->id,
            'invoiceNumber' => self::generateFormattedInvoiceNumber($servicePayment, 'service'),
            'quantity' => 1,
            /* TODO: need to pass 2nd parameter in "AccountCodeForServicePayment" campus id for student who made this  miscellaneous payment */
            'accountCode' => XeroConfig::AccountCodeForServicePayment(@$servicePayment, XeroConfig::ACCOUNT_AIRPORT_PICKUP_FEE, @$servicePayment->student->collegeCampus->id),
            'discountRate' => 0.0,
            'status' => Invoice::STATUS_DRAFT,
            'lineAmountTypes' => 'Exclusive',
            // 'description' => ($description ? $description : 'Student Service Payment (' . @$servicePayment->course->course_code . ')'),
            'description' => $serviceDescription,
            'unitdp' => 4,
            'amount' => @$servicePayment->amount,
            'date' => (new DateTime($servicePayment->created_at))->format('Y-m-d'),
            // 'date' => (new DateTime(@$initialPaymentDetail->invoiced_start_date))->format("Y-m-d"),
            'dueDate' => $servicePayment->due_date ? (new DateTime(@$servicePayment->due_date))->format('Y-m-d') : null,
        ], $extra));
    }

    public static function FromScholarship(StudentScholarship $scholarship, $extra = [])
    {
        // $description = @$scholarship->additionalServiceDetail->short_description;
        $scholarshipDescription = sprintf(
            'Scholarship (%s) for "%s: %s"',
            @$scholarship->additionalServiceDetail->short_description,
            @$scholarship->course->course_code,
            @$scholarship->course->course_name
        );

        return InvoicePayload::LazyFromArray(array_merge([
            // 'invoiceNumber' => 'GAL-CRN-SCHP-' . $scholarship->id,
            'invoiceNumber' => self::generateFormattedInvoiceNumber($scholarship, 'scholarship'),
            'quantity' => 1,
            /* TODO: need to pass 2nd parameter in "AccountCodeForscholarship" campus id for student who made this  miscellaneous payment */
            'accountCode' => XeroConfig::AccountCode(XeroConfig::ACCOUNT_CREDITNOTE),
            'discountRate' => 0.0,
            'status' => Invoice::STATUS_DRAFT,
            'lineAmountTypes' => 'Exclusive',
            // 'description' => ($description ? $description : 'Student Scholarship (' . @$scholarship->course->course_code . ')'),
            'description' => $scholarshipDescription,
            'unitdp' => 4,
            'amount' => @$scholarship->scholarship_amount,
            'date' => (new DateTime($scholarship->created_at))->format('Y-m-d'),
            // 'date' => (new DateTime(@$initialPaymentDetail->invoiced_start_date))->format("Y-m-d"),
            'dueDate' => null,
        ], $extra));
    }

    public static function generateFormattedInvoiceNumber($object, $type = '')
    {
        /*if ($type == 'schedule') {
            return 'GAL-' . $object->student->generated_stud_id . '-' . $object->course->course_code . '-AG' . $object->agent_id . '-' . $object->invoice_number;
        } elseif ($type == 'agent_commission') {
            return 'GAL-' . $object->student->generated_stud_id .'-'. $object->course->course_code .'-AG'. $object->agent_id .'-'. $object->invoice_number .'-'. $object->id;
        } elseif ($type == 'miscellaneous') {
            return 'GAL-MISC-' . $object->student->generated_stud_id . '-' . $object->course->course_code . '-' . $object->invoice_number;
        } elseif ($type == 'service') {
            return 'GAL-SRV-' . $object->student->generated_stud_id . '-' . $object->course->course_code . '-' . $object->invoice_number;
        } elseif ($type == 'scholarship') {
            return 'GAL-CRN-SCHP-' . $object->id;
        } else {
            return 'GAL-' . $object->student->generated_stud_id . '-' . $object->course->course_code . '-' . $object->invoice_number;
        }*/

        $base = 'GAL-';
        $studentId = $object->student->generated_stud_id;
        $courseCode = $object->course->course_code;
        $invoiceNumber = ($type == 'agent_commission') ? $object->invoice_no : $object->invoice_number;

        switch ($type) {
            case 'schedule':
                return "{$base}{$studentId}-{$courseCode}-AG{$object->agent_id}-{$invoiceNumber}";
            case 'agent_commission':
                return "{$base}{$studentId}-{$courseCode}-AG{$object->agent_id}-{$invoiceNumber}-{$object->id}";
            case 'miscellaneous':
                return "{$base}MISC-{$studentId}-{$courseCode}-{$invoiceNumber}";
            case 'service':
                return "{$base}SRV-{$studentId}-{$courseCode}-{$invoiceNumber}";
            case 'scholarship':
                return "{$base}CRN-SCHP-{$object->id}";
            default:
                return "{$base}{$studentId}-{$courseCode}-{$invoiceNumber}";
        }
    }

    public static function GetTrackingForAgent(Agent $agent)
    {
        $category = XeroConfig::Factory()->getAgentTrackingCategory();
        if ($agent && $category && $category->Name) {
            return [
                'Name' => $category->Name,
                'Option' => $agent->formatted_name,
            ];
        }
    }
}
