<script id="viewSchedulePaymentInfoTemplate" type="text/html">
    <div
        class="inline-flex flex-col items-start justify-start px-6 py-4 bg-white border rounded-lg border-gray-200 w-full mt-4">
        <div class="flex flex-col items-center justify-start w-full">
            <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Invoice Number</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: formatted_invoice_number #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Invoice Start Date</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: convertJsDateFormat(invoiced_start_date) #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Payment Status</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: capitalizeFirstCharacter(payment_status) #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Fee Payable</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: kendo.toString(upfront_fee_to_pay, 'c') #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Fee Paid</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: kendo.toString(upfront_fee_pay, 'c') #</p>
                </div>
            </div>
            {{-- #if(xero_connect == 0){ } #--}}
            <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Invoice Credit</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: kendo.toString(parseFloat(invoice_credit), 'c') #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Fee Duration</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: fee_duration #</p>
                </div>
            </div>
        </div>
    </div>
    #if(typeof(xero_invoice) != "undefined" && xero_invoice !== null){ #
    <div>
        <div class="flex flex-col items-start justify-start w-full py-4">
            <p class="text-sm font-medium leading-5 text-gray-700">Xero Details</p>
        </div>
        <div
            class="inline-flex flex-col items-start justify-start px-6 py-4 bg-white border rounded-lg border-gray-200 w-full mb-6">
            <div class="flex flex-col items-center justify-start w-full">
                <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="w-64 text-sm font-medium leading-5 text-gray-500">Xero ID</p>
                        # let xero_id = (typeof(xero_invoice) != "undefined" && xero_invoice !== null) ?
                        xero_invoice.xero_invoice_id : 'No Data Found'; #
                        <p class="text-sm leading-5 text-gray-900 w-full">#: xero_id #</p>
                    </div>
                </div>
                <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="w-64 text-sm font-medium leading-5 text-gray-500">Xero Sync Status</p>
                        # let xero_invoice_status = (typeof(xero_invoice) != "undefined" && xero_invoice !== null) ?
                        xero_invoice.xero_invoice_status : 'No Data Found'; #
                        <p class="text-sm leading-5 text-gray-900 w-full">#: xero_invoice_status #</p>
                    </div>
                </div>
                <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="w-64 text-sm font-medium leading-5 text-gray-500">Last Synced Successfully</p>
                        # let xero_synced_at = (typeof(xero_invoice) != "undefined" && xero_invoice !== null && xero_invoice.xero_synced_at != null) ? convertJsDateTimeFormat(xero_invoice.xero_synced_at) : 'No Data Found'; #
                        <p class="text-sm leading-5 text-gray-900 w-full">#: xero_synced_at #</p>
                    </div>
                </div>
                #if(xero_invoice.xero_failed_at){#
                <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="w-64 text-sm font-medium leading-5 text-gray-500">Last Error</p>
                        <p class="text-sm leading-5 text-gray-900 w-full">#: convertJsDateTimeFormat(xero_invoice.xero_failed_at) #</p>
                    </div>
                </div>
                #}#
                #if(xero_invoice.xero_failed_message){#
                <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="w-64 text-sm font-medium leading-5 text-gray-500">Error Message</p>
                        <p class="text-sm leading-5 text-gray-900 w-full">#: xero_invoice.xero_failed_message #</p>
                    </div>
                </div>
                #}#
                <div class="flex flex-col items-start justify-center py-3 w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="w-64 text-sm font-medium leading-5 text-gray-500">Reference</p>
                        # let xero_reference = (typeof(xero_invoice.xero_data.xero_details) != "undefined" &&
                        typeof(xero_invoice.xero_data.xero_details.Reference) != "undefined" &&
                        xero_invoice.xero_data.xero_details.Reference !== null) ?
                        xero_invoice.xero_data.xero_details.Reference : '--'; #
                        <p class="text-sm leading-5 text-gray-900 w-full">#: xero_reference #</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    # } #

    #if(xero_connect == 1 && is_student_sync == 1){ #
    <div class="flex items-start justify-end w-full py-4">
        #if(typeof(xero_invoice) != "undefined" && xero_invoice !== null && xero_invoice.xero_invoice_id){ #
        <div class="flex justify-end space-x-4">
            <button type="button" data-id="#: id #" title="#: xero_invoice.xero_synced_at #"
                class="syncToXeroPaymentScheduleBtn glob-tooltip btn-primary bg-green-300 text-white hover:bg-green-500"
                disabled>
                <span class="tw-icon tw-icon--sync"></span>
                <p class="text-xs leading-4 text-gray-700 uppercase">SYNCED TO XERO</p>
            </button>
            #if(xero_invoice.xero_failed_at){#
            <button type="button" data-id="#: id #" title="Sync"
                class="syncFromXeroPaymentScheduleBtn glob-tooltip btn-primary">
                <span class="k-icon fail-sync-icon"></span>
                <p class="text-xs leading-4 text-white uppercase">SYNC FROM XERO</p>
            </button>
            #}#
        </div>
        # } else { #
        <div class="space-x-4">
            <button type="button" data-id="#: id #" title="Sync"
                class="syncToXeroPaymentScheduleBtn glob-tooltip btn-secondary">
                <span class="tw-icon tw-icon--sync"></span>
                <p class="text-xs leading-4 text-gray-700 uppercase">SYNC TO XERO</p>
            </button>
        </div>
        # } #
    </div>
    # } #
</script>

<script id="viewPaymentScheduleDetailsTemplate" type="text/html">
    <div
        class="inline-flex flex-col items-start justify-start px-6 py-4 bg-white border rounded-lg border-gray-200 w-full mt-4">
        <div class="flex flex-col items-center justify-start w-full">
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Trans No</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: transection_no #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Receipt No</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: receipt_no #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Invoice ID</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: invoice_number #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Payment Date</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: payment_date #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Paid Amount</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: kendo.toString(paid_amount, 'c') #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Deposited Amount</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: kendo.toString(deposited_amount, 'c') #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Pay Mode</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: payment_mode #</p>
                </div>
            </div>
            {{-- <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Cheque No.</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: fee_duration #</p>
                </div>
            </div> --}}
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Remarks</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: displayOrDefault(remarks) #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Bank Deposite Date</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: bank_deposit_date #</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-3 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-64 text-sm font-medium leading-5 text-gray-500">Refund Amount</p>
                    <p class="text-sm leading-5 text-gray-900 w-full">#: kendo.toString(amount_refund, 'c') #</p>
                </div>
            </div>
        </div>
    </div>
</script>

{{-- Payment grid actions --}}
<script id="paymentScheduleActionTemplate" type="text/html">
    <div class="action-menu">
        <div class="px-1 space-y-1 py-2">
            <button type="button" data-id="#=id#"
                class="editSchedulePaymentInfoBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <span class="k-icon k-i-edit k-icon-edit"></span>
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Modified Payment</span>
            </button>
            #if(commission > 0){#
            <button type="button" data-id="#=id#"
                class="generateAgentInvSinglePayBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/agent-invoice.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Generate Agent Invoice</span>
            </button>
            # } #
            <button type="button" data-id="#=id#"
                class="generateScheduleInvSinglePayBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/upfront-fee.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Generate Schedule Invoice</span>
            </button>
            #if(is_allow_agent_invoice){#
            <button type="button" data-id="#=id#"
                    class="generateAgentTaxReceiptBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/agent-invoice.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Generate Agent Receipt</span>
            </button>
            #}#
            <button type="button" data-id="#=id#"
                class="paymentScheduleSendEmailBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/mail.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Send Email</span>
            </button>
            <button type="button" data-id="#=id#"
                class="viewStudentSchedulePaymentInfoBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/offer-check.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">View Schedule Payment
                    Info</span>
            </button>

            # if(payment_status == 'unpaid'){ #
            #if(xero_connect == 1 && xero_invoice != null && is_student_sync == 1){ #
            #if(xero_invoice.xero_invoice_status == "DRAFT"){ #
            <button type="button" data-id="#=id#"
                class="deletePaymentScheduleBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/delete-gray.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Delete Record</span>
            </button>
            #}#
            #}else{#
            <button type="button" data-id="#=id#"
                class="deletePaymentScheduleBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/delete-gray.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Delete Record</span>
            </button>
            #}#
            #}#

            #if(xero_connect == 1 && is_student_sync == 1){ #
            #if(xero_invoice == null){ #
            <button type="button" data-id="#=id#"
                class="syncToXeroPaymentScheduleBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/offer-check.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Sync To Xero</span>
            </button>
            #} #
            #} #

            #if(payment_status != "paid" && xero_connect == 0){ #
            <button type="button" data-id="#=id#"
                class="addInvoiceCreditBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/edit-offer-id.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Invoice Credit</span>
            </button>
            #} #
        </div>
    </div>
</script>

{{-- History grid actions --}}
<script id="paymentHistoryActionTemplate" type="text/html">
    <div class="action-menu">
        <div class="px-1 space-y-1 py-2">
            #if(is_reversed == "1"){ #
            <button type="button" data-id="#=id#"
                class="paymentHistoryRevertPayment flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/arrow-clockwise.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Revert Payment</span>
            </button>
            <button type="button" data-id="#=id#"
                class="paymentHistorySendEmailBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/mail.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Send Email</span>
            </button>
            # }else{ #

            #if(typeof(xero_payment_id) == "undefined" || xero_payment_id == null){ #
            <button type="button" data-id="#=id#"
                class="editPaymentHistoryBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <span class="k-icon k-i-edit k-icon-edit"></span>
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Modified Payment</span>
            </button>
            <button type="button" data-id="#=id#"
                    data-invoice="#=invoiceNumber#"
                    data-formatted-invoice="#=formatted_invoice_number#"
                    data-paid_amount="#=paid_amount#"
                    data-deposited_amount="#=deposited_amount#"
                    data-payment_mode="#=payment_mode#"
                    data-payment_date="#=payment_date#"
                class="paymentTransactionReverseBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/arrow-swap.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Reverse Transaction</span>
            </button>
            <button type="button" data-id="#=id#"
                class="feeRefundDetailsBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/arrow-clockwise.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Refund Payment</span>
            </button>
            # } #
            <button type="button" data-id="#=id#"
                class="generateStudentTaxReceiptBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/upfront-fee.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Generate Student Receipt</span>
            </button>
            #if(typeof(xero_payment_id) == "undefined" || xero_payment_id == null){ #
            <button type="button" data-id="#=id#"
                class="deletePaymentTransactionBtn flex w-full cursor-pointer justify-start space-x-2 rounded px-2 py-1 hover:bg-gray-100 items-center text-left">
                <img src="{{ asset('v2/img/delete-gray.svg') }}" class="w-4 h-4 grow-1 shrink-0" alt=" ">
                <span class="text-xs leading-5 text-gray-700 hover:text-gray-900 w-full">Delete Transaction</span>
            </button>
            #}#
            #}#
        </div>
    </div>
</script>

<div id="viewSchedulePaymentInfoModal" style="display: none;">
    <div class="flex h-auto min-h-screen w-full flex-col bg-gray-100 p-6">
        <div class="paymentScheduleViewAllInfoList flex w-full rounded-md bg-white">
            <div id="paymentScheduleViewAllInfoList" class="flex tw-table tw-table__bordered--rounded">
            </div>
        </div>
        <div id="viewPaymentScheduleInfo" class="flex w-full flex-col">
        </div>
    </div>
</div>

{{-- Incoice Credit Modal --}}
<div id="addInvoiceCreditModal" class="wizardModal" style="display: none">
    <div class="inline-flex w-full flex-col items-center justify-start space-y-4 px-6 pt-4">
        <form id="addInvoiceCreditForm" class="w-full" method="POST" accept-charset="UTF-8"
            enctype="multipart/form-data">
        </form>
    </div>
    <div class="inline-flex w-full flex-col items-center justify-start space-y-4 px-6 pb-4">
        <div class="border-l border-r border-t border-gray-200">
            <div id="addInvoiceCreditList"></div>
        </div>
    </div>
    <div class="inline-flex w-full items-center justify-between space-x-4 border-t bg-white p-6">
        <div></div>
        <div class="flex items-center justify-end space-x-2">
            <button
                class="cancelBtn flex h-full items-center justify-center rounded-lg border border-gray-300 bg-white px-4 py-2 shadow hover:shadow-xl focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white"
                type="button" data-id="addInvoiceCreditModal">
                <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
            </button>
            <button id=""
                class="saveInvoiceCredit flex h-full items-center justify-center rounded-lg rounded-lg bg-primary-blue-500 px-4 py-2 shadow hover:shadow-xl focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white"
                data-form="addInvoiceCreditModal" type="button">
                <p class="text-sm font-medium leading-4 text-white">ADD INVOICE CREDIT</p>
            </button>
        </div>
    </div>
</div>

<div id="deletePaymentTransactionModal" class="wizardModal !bg-gray-50" style="display: none">
    <div class="space-y-4 px-6 py-4 bg-gray-50">
        <div class="text-sm font-medium text-gray-700">Are you sure you want to delete this item?</div>
        <div
            class="px-6 py-2 bg-white border rounded-lg border-gray-200 w-full">
            <x-v2.label-value class="grid-cols-[250px_1fr]" label="Transaction No">
                <span class="transactionNo"></span>
            </x-v2.label-value>
            <x-v2.label-value class="grid-cols-[250px_1fr]" label="Receipt No">
                <span class="receiptNo"></span>
            </x-v2.label-value>
            <x-v2.label-value class="grid-cols-[250px_1fr]" label="Payment Date">
                <span class="paymentDate"></span>
            </x-v2.label-value>
            <x-v2.label-value class="grid-cols-[250px_1fr]" label="Paid Amount">
                <span class="totalPaidAmount"></span>
            </x-v2.label-value>
            <x-v2.label-value class="grid-cols-[250px_1fr]" label="Refund Amount">
                <span class="refundAmount"></span>
            </x-v2.label-value>
<!--            <x-v2.label-value class="grid-cols-[250px_1fr]" label="Commission Payable (GST Inc)">
                <span class="commissionPayable"></span>
            </x-v2.label-value>
            <x-v2.label-value class="grid-cols-[250px_1fr]" label="Commission Paid (GST Inc)">
                <span class="commissionPaid"></span>
            </x-v2.label-value>-->
            <x-v2.label-value class="grid-cols-[250px_1fr]" label="Remarks">
                <span class="remarks"></span>
            </x-v2.label-value>
        </div>
        <form id="deletePaymentHistoryForm" class="w-full pb-6"></form>
    </div>
</div>

<div id="feeRefundDetailModal" class="wizardModal" style="display: none">
    <div class="flex p-6 bg-gray-100 w-full h-full">
        <div
            class="inline-flex flex-col items-start justify-start px-6 py-4 bg-white border rounded-lg border-gray-200 w-full paymentRefundOverFlow">
            <div class="flex flex-col items-center justify-start w-full space-y-2">
                <div class="flex flex-col items-start justify-center w-full">
                    <p class="text-base font-medium leading-6 text-gray-900 w-2/3">Student Refund Details</p>
                </div>
                <form id="studentRefundDetailsForm" class="w-full"></form>
                <div class="flex flex-col items-start justify-center w-full">
                    <p class="text-base font-medium leading-6 text-gray-900 w-2/3">Agent Refund Details</p>
                </div>
                <form id="agentRefundDetailsForm" class="w-full"></form>
            </div>
        </div>
    </div>
    <div class="inline-flex w-full items-center justify-between space-x-4 border-t bg-white p-6">
        <div></div>
        <div class="flex items-center justify-end space-x-2">
            <button
                class="cancelBtn flex h-full items-center justify-center rounded-lg border border-gray-300 bg-white px-4 py-2 shadow hover:shadow-xl focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white"
                type="button" data-id="feeRefundDetailModal">
                <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
            </button>
            <button id=""
                class="feeRefundSave flex h-full items-center justify-center rounded-lg rounded-lg bg-primary-blue-500 px-4 py-2 shadow hover:shadow-xl focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white"
                data-form="addInvoiceCreditModal" type="button">
                <p class="text-sm font-medium leading-4 text-white">REFUND</p>
            </button>
        </div>
    </div>
</div>

<div id="paymentTransactionReverseModal" class="wizardModal" style="display: none">
    <form id="paymentTransactionReverseForm" method="POST" accept-charset="UTF-8" enctype="multipart/form-data">
        <div class="flex p-6 bg-gray-100 w-full h-full">
            <div
                class="inline-flex flex-col items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
                <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="text-sm font-medium leading-5 text-gray-500 w-2/3">Invoice Number</p>
                        <p class="text-sm leading-5 text-gray-900 w-full invoice"></p>
                    </div>
                </div>
                <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="text-sm font-medium leading-5 text-gray-500 w-2/3">Paid Amount</p>
                        <p class="text-sm leading-5 text-gray-900 w-full paid_amount"></p>
                    </div>
                </div>
                <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="text-sm font-medium leading-5 text-gray-500 w-2/3">Deposited Amount</p>
                        <p class="text-sm leading-5 text-gray-900 w-full deposited_amount"></p>
                    </div>
                </div>
                <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="text-sm font-medium leading-5 text-gray-500 w-2/3">Payment Mode</p>
                        <p class="text-sm leading-5 text-gray-900 w-full payment_mode"></p>
                    </div>
                </div>
                <div class="flex flex-col items-start justify-center py-3 border-b w-full">
                    <div class="inline-flex space-x-4 items-center justify-end w-full">
                        <p class="text-sm font-medium leading-5 text-gray-500 w-2/3">Payment date</p>
                        <p class="text-sm leading-5 text-gray-900 w-full payment_date"></p>
                    </div>
                </div>
                <div class="flex flex-col items-start justify-center py-3 w-full">
                    <div class="inline-flex flex-col items-start space-y-2 justify-end w-full">
                        <p class="text-sm font-medium leading-5 text-gray-500 w-2/3">Reverse Comment</p>
                        <div class="w-full">
                            <textarea id="reverse_comment" name="reverse_comment" required></textarea>
                        </div>
                        <input type="hidden" name="transaction_id" id="transaction_id" value="" />
                    </div>
                </div>
            </div>
        </div>
        <div class="inline-flex space-x-4 items-center justify-between p-6 bg-white w-full border-t">
            <div>
            </div>
            <div class="flex space-x-2 items-center justify-end">
                <button
                    class="cancelBtn flex items-center justify-center h-full px-4 py-2 bg-white shadow border hover:shadow-xl rounded-lg border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400"
                    type="button" data-id="paymentTransactionReverseModal">
                    <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
                </button>
                <button id=""
                    class="paymentTransactionReverse flex h-full items-center justify-center rounded-lg rounded-lg bg-primary-blue-500 px-4 py-2 shadow hover:shadow-xl focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white"
                    data-form="addInvoiceCreditModal" type="button">
                    <p class="text-sm font-medium leading-4 text-white">REVERSE NOW</p>
                </button>
            </div>
        </div>
    </form>
</div>

<div id="editSchedulePaymentInfoModal" style="display: none">
    <div class="flex w-full items-start justify-start space-y-6 bg-gray-100 p-6">
        <div
            class="inline-flex w-full flex-col items-start justify-start space-y-6 rounded-lg border border-gray-200 bg-white p-5">
            <input id="editSchedulePaymentInfoId" hidden name="editSchedulePaymentInfoId" />
            <input id="edit_agent_id" hidden name="agent_id" />
            <input id="edit_hidden_agent_commission" hidden name="commission_value" />
            <input id="payment_status" hidden name="payment_status" />
            <input id="upfront_fee_pay" hidden name="upfront_fee_pay" />
            <input id="hide_course_start_date" hidden name="hide_course_start_date" />
            <form id="editPaymentScheduleForm" class="w-full" method="POST" accept-charset="UTF-8"
                enctype="multipart/form-data">

            </form>
        </div>
    </div>
    <div
        class="modal-footer sticky bottom-0 right-0 flex w-full items-center justify-end space-x-4 border-t bg-white px-6 py-5">
        <div class="float-right flex items-center justify-end space-x-4">
            <button type="button"
                class="cancelBtn flex justify-center rounded-lg border border-gray-300 bg-white px-6 py-2 shadow hover:shadow-md focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white"
                fdprocessedid="tsnonk">
                <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
            </button>
            <button type="button"
                class="editSchedulePayment flex h-full justify-center rounded-lg bg-primary-blue-500 px-4 py-2 hover:shadow-md focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white"
                fdprocessedid="ieerk">
                <p class="text-sm font-medium leading-4 text-white">EDIT PAYMENT SCHEDULE</p>
            </button>
        </div>
    </div>
</div>

<div id="editPaymentHistoryModal" style="display: none">
    <div class="flex w-full items-start justify-start space-y-6 bg-gray-100 p-6">
        <div
            class="inline-flex w-full flex-col items-start justify-start space-y-6 rounded-lg border border-gray-200 bg-white p-5">
            <form id="editPaymentHistoryForm" class="w-full" method="POST" accept-charset="UTF-8"
                enctype="multipart/form-data">

            </form>
        </div>
    </div>
    <div
        class="modal-footer fixed bottom-0 right-0 flex w-full items-center justify-end space-x-4 border-t bg-white px-6 py-5">
        <div class="float-right flex items-center justify-end space-x-4">
            <button type="button"
                class="cancelBtn flex justify-center rounded-lg border border-gray-300 bg-white px-6 py-2 shadow hover:shadow-md focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white"
                fdprocessedid="tsnonk">
                <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
            </button>
            <button type="button"
                class="editPaymentHistory flex h-full justify-center rounded-lg bg-primary-blue-500 px-4 py-2 hover:shadow-md focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white"
                fdprocessedid="ieerk">
                <p class="text-sm font-medium leading-4 text-white">EDIT PAYMENT TRANSACTION</p>
            </button>
        </div>
    </div>
</div>

<script type="text/html" id="recordPaymentButton">
    <button type="button" data-id="#=id#" class="recordPaymentBtn tw-btn-action text-xs h-6 w-6 glob-tooltip" title="Record Payment">
        <img src="{{ asset('v2/svg/icon-record-payment.svg') }}" class="w-4 h-4 grow-1 shrink-0 mx-auto" alt=" ">
    </button>
</script>

<div id="deletePaymentScheduleModal" class="!bg-gray-50" style="display: none;">
    <div class="space-y-4">
        <div class="text-sm font-medium text-gray-700">Are you sure you want to delete this transactions?</div>
        <div class="inline-flex flex-col items-start justify-start px-6 py-4 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col items-center justify-start w-full">
                <x-v2.label-value label="Trans No">
                    <span class="trans_no">25</span>
                </x-v2.label-value>
                <x-v2.label-value label="Payment Amount">
                    <span class="payment_amount">$500</span>
                </x-v2.label-value>
                <x-v2.label-value label="Due Date">
                    <span class="due_date">11 Oct 2021</span>
                </x-v2.label-value>
                <x-v2.label-value label="Remarks">
                    <span class="remarks">Need to pay</span>
                </x-v2.label-value>
            </div>
            <input type="text" hidden name="id" id="transactionId"/>

        </div>
        <div id="deleteInstallmentPaymentForm" class="w-full pb-8">
        </div>
    </div>
</div>

<div id="syncToXeroPaymentScheduleModal"></div>

<div id="syncFromXeroPaymentScheduleModal"></div>

<div id="syncToXeroForBulkPaymentModalId"></div>

<div id="deleteInvoiceCreditModal"></div>

<!-- <div id="bulkDeleteItemModal"></div> -->
<div id="bulkDeleteItemModal" style="display: none;">
    <div class="inline-flex flex-col space-y-4 items-start justify-start bg-gray-100 p-6 w-full">
        <div class="text-sm font-medium text-gray-700">Are you sure you want to delete selected transaction?</div>
        <div class="flex flex-col space-y-4 items-start justify-start p-4 bg-white border rounded-lg border-gray-200 w-full">
            <x-v2.label-value label="Inv No" class="isInvoiceNumber grid-cols-[80px_1fr]">
                <span id="bulkInvoiceIds" class="bulkInvoiceIds"></span>
            </x-v2.label-value>
            <x-v2.label-value label="Scholarship Name" class="isScholarship">
                <span id="bulkInvoiceIds" class="bulkInvoiceIds"></span>
            </x-v2.label-value>
            {{-- <div class="isInvoiceNumber">
                    <span class="k-label k-form-label">Invoice Numbers</span>
                </div>
                <div class="isScholarship">
                    <span class="k-label k-form-label">Scholarship Name</span>
                </div>
                <div class="flex-col w-full">
                    <div class="flex w-full break-all" id="bulkInvoiceIds"></div>
                </div> --}}
            <div class='flex k-form-field mt-10 w-full'>
                <label class='k-label k-form-label' for='reason' id='reason-form-label'>Reason for delete ?</label>
                <textarea class='customTextField' required type='text' name='reason' id='reason' rows='4'></textarea>
            </div>
            <div class="flex items-center gap-2 mt-10 w-full" id="isAllowForceDeleteInv">
                <input type="checkbox" value="1" name="is_allow_force_delete_inv" id="is_allow_force_delete_inv">
                <label for="is_allow_force_delete_inv" class="text-red-500">
                    <p>Checking this box will remove the invoice from the galaxy system.</p>
                    <p>If it has already been synced to Xero, you’ll need to manage any further changes or actions directly in Xero.</p>
                </label>
            </div>
            <input type='hidden' name='id' id='bulkDeleteItemIds' />
        </div>
    </div>
    <div
        class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-5 bottom-0 right-0 border-t bg-white px-6">
        <div class="float-right flex space-x-4 items-center justify-end">
            <button type="button" class="btn-secondary px-6 py-2 cancelBtn">
                <p class="text-sm font-medium leading-4 text-gray-700">Cancel</p>
            </button>
            <button type="button" class="bulkDeleteModalBtn btn-danger px-3 py-2" title="Confirm Delete">
                <p class="text-sm font-medium leading-4 text-white">Delete Transactions</p>
            </button>
        </div>
    </div>

</div>
