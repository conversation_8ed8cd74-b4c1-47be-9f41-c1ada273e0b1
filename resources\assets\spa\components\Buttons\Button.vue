<template lang="">
    <component
        :is="type === 'link' ? 'a' : 'button'"
        v-bind="$attrs"
        :class="rootClass"
        :type="type !== 'link' ? type : undefined"
        :disabled="loading || disabled"
    >
        <template v-if="loading">
            <template v-if="$slots.loader">
                <slot name="loader" />
            </template>
            <template v-else>
                <span><icon name="loading-spinner" width="16" height="16" fill="#1890ff" /></span>
                <span v-if="loadingText == ''"> {{ defaultLoadingText }} </span>
                <span v-else> {{ loadingText }} </span>
            </template>
        </template>
        <template v-else>
            <slot />
        </template>
    </component>
</template>
<script>
import { twMerge } from 'tailwind-merge';
import Spinner from '@spa/components/Loader/Spinner.vue';
export default {
    props: {
        variant: String,
        size: String,
        className: String,
        loading: { type: Boolean, default: false },
        disabled: { type: Boolean, default: false },
        loadingText: { type: String, default: '' },
        target: { type: String, default: '' },
        type: { type: String, default: 'button' },
        outline: { type: Boolean, default: false },
        autoHide: {
            type: Boolean,
            default: true,
        },
        pt: {
            type: Object,
            default: {},
        },
    },
    components: {
        Spinner,
    },
    computed: {
        rootClass() {
            return twMerge(
                this.getVariantClass(this.variant),
                this.getSizeClass(this.size),
                this.className,
                this.pt.root
            );
        },
        defaultLoadingText() {
            if (this.variant === 'icon') {
                return '';
            } else {
                return 'Loading...';
            }
        },
    },
    methods: {
        getVariantClass(variant) {
            return (
                {
                    primary: 'tw-btn-primary',
                    secondary: 'tw-btn-secondary',
                    tertiary: 'tw-btn-tertiary',
                    text: 'tw-btn-text',
                    danger: 'tw-btn-danger',
                    action: twMerge('tw-btn-action', this.autoHide ? 'tw-action--autohide' : ''),
                    success: 'tw-btn-success',
                    icon: 'tw-btn-icon',
                    ghost: 'tw-btn-ghost',
                    default: 'tw-btn-default',
                }[variant] || 'tw-btn-primary'
            );
        },
        getSizeClass(size) {
            return (
                {
                    xxs: 'h-7',
                    xs: 'h-[1.87rem] leading-[1.87rem]',
                    sm: 'h-9 leading-[34px]',
                    base: 'h-10 leading-10',
                    lg: 'h-11 leading-11',
                    xl: 'h-12 leading-12',
                    auto: 'h-auto',
                }[size] || 'h-10 leading-10'
            );
        },
    },
};
</script>
<style lang=""></style>
