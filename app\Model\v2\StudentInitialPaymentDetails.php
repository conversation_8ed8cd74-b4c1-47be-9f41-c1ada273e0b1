<?php

namespace App\Model\v2;

use App\Observers\StudentsObserver;
use Domains\Xero\Traits\XeroableInvoice;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class StudentInitialPaymentDetails extends Model
{
    use HasFactory;
    use LogsActivity;
    use SoftDeletes;
    use XeroableInvoice;

    const STATUS_PAID = 'paid';

    const STATUS_UNPAID = 'unpaid';

    const STATUS_PARTIALLY_PAID = 'partially paid';

    const PAYMENT_TYPE_INITIAL = 'Initial';

    const PAYMENT_TYPE_SCHEDULE = 'Schedual';

    const STATUS_NOT_SYNC = 0;

    const STATUS_SYNCED = 1;

    const STATUS_SYNCING = 2;

    const STATUS_AUTHORISED = 3;

    protected $table = 'rto_student_initial_payment_details';

    protected $appends = ['formatted_invoice_number'];

    protected $fillable = [
        'college_id',
        'student_id',
        'course_id',
        'student_course_id',
        'invoice_number',
        'invoiced_start_date',
        'due_date',
        'agent_id',
        'upfront_fee_to_pay',
        'upfront_fee_pay',
        'accrued_fee',
        'paid_duration_text',
        'paid_duration_day',
        'commission_value',
        'commission',
        'GST',
        'agent_bonus',
        'bonus_gst_amount',
        'bonus_gst',
        'bonus_paid_date',
        'bad_debt',
        'gst_amount',
        'invoice_sent',
        'invoice_credit',
        'remarks',
        'payment_type',
        'payment_status',
        'payment_mode',
        'refunded_amount',
        'scholarship_amount',
        'is_synced',
        // 'created_at',
        // 'updated_at',
        'created_by',
        'updated_by',
    ];

    protected static $logAttributes = [
        'course_id',
        'invoice_number',
        'invoiced_start_date',
        'due_date',
        'agent_id',
        'upfront_fee_to_pay',
        'upfront_fee_pay',
        'accrued_fee',
        'paid_duration_text',
        'paid_duration_day',
        'commission_value',
        'commission',
        'GST',
        'agent_bonus',
        'bonus_gst_amount',
        'bonus_gst',
        'bonus_paid_date',
        'bad_debt',
        'gst_amount',
        'invoice_sent',
        'invoice_credit',
        'remarks',
        'payment_type',
        'payment_status',
        'payment_mode',
        'refunded_amount',
        'scholarship_amount',
        'is_synced',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Payment Schedule has been {$eventName}");
        // Chain fluent methods for configuration options
    }

    /* This method is fired before saving the log to db. */
    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = $this->xeroLogKey();
        if (! empty($eventName)) {
            $identifier = $this->formatted_invoice_number;
            if ($eventName != 'created' && isset($activity->properties['attributes']['payment_status'])) {
                $this->handlePaymentStatus($activity, $identifier);
            } elseif ($eventName == 'updated' && isset($activity->properties['attributes']['is_synced'])) {
                $this->handleSyncStatus($activity, $identifier, $eventName);
            } else {
                $this->handleOtherEvents($activity, $identifier, $eventName);
            }
        }
    }

    protected function handlePaymentStatus(Activity $activity, string $identifier)
    {
        $activity->description = 'Payment Schedule '.$identifier.' has been '.$activity->properties['attributes']['payment_status'];
    }

    protected function handleSyncStatus(Activity $activity, string $identifier, string $eventName)
    {
        $syncStatus = $activity->properties['attributes']['is_synced'];
        switch ($syncStatus) {
            case 1:
                $activity->description = "Payment Schedule $identifier has been Synced.";
                $this->updateProperties($activity, 'is_synced', 'Synced', 'Syncing');
                break;
            case 2:
                $activity->description = "Payment Schedule $identifier has been Syncing.";
                $this->updateProperties($activity, 'is_synced', 'Syncing', 'Not Sync');
                break;
            case 3:
                $activity->description = "Payment Schedule $identifier has been Authorised.";
                $this->updateProperties($activity, 'xero_status', 'AUTHORISED', 'Draft');
                break;
        }
    }

    protected function updateProperties(Activity $activity, string $fieldName, string $newValue, string $oldValue)
    {
        $activity->properties = $activity->properties
            ->put('attributes', [$fieldName => $newValue])
            ->put('old', [$fieldName => $oldValue]);
    }

    protected function handleOtherEvents(Activity $activity, string $identifier, string $eventName)
    {
        $activity->description = 'Payment Schedule '.$identifier.' has been '.$eventName;

        if ($eventName == 'deleted' && request('reason')) {
            $activity->description .= ' <br><strong>REASON:</strong> '.request('reason');
        }
    }

    public function getDescriptionForEvent(string $eventName): string
    {
        return "Payment Schedule has been {$eventName}";
    }

    public function isInitial()
    {
        return strtolower($this->payment_type) == 'initial';
    }

    public function course()
    {
        return $this->belongsTo(Courses::class, 'course_id');
    }

    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function agent()
    {
        return $this->belongsTo(Agent::class, 'agent_id');
    }

    public function transactions()
    {
        return $this->hasMany(StudentInitialPaymentTransaction::class, 'initial_payment_detail_id');
    }

    public function activeTransactions()
    {
        return $this->hasMany(StudentInitialPaymentTransaction::class, 'initial_payment_detail_id')->where('is_delete', 0);
    }

    public function miscellaneousPayments()
    {
        return $this->hasMany(StudentMiscellaneousPayment::class, 'invoice_number', 'invoice_number');
    }

    // TODO:: please verify payment status
    public function markAsPaid($status = 'paid')
    {
        $this->payment_status = $status;

        return $this->save();
    }

    public function selfDestruct()
    {
        $this->transactions()->delete();

        return $this->delete();
    }

    public function syncScheduleAmountAndStatusData($payableAmount, $paidAmount = 0, $syncStatus = '', $invoiceCredit = 0)
    {
        if ($payableAmount > 0) {
            if ($paidAmount + $invoiceCredit >= $payableAmount) { // TODO::GNG-4773 before using ($paidAmount >= $payableAmount)
                $this->payment_status = self::STATUS_PAID;
            } elseif ($paidAmount > 0) {
                $this->payment_status = self::STATUS_PARTIALLY_PAID;
            } else {
                $this->payment_status = self::STATUS_UNPAID;
            }

            $this->upfront_fee_pay = $paidAmount;
            $this->bonus_gst_amount = 0;

            if ($paidAmount == 0 && $syncStatus == self::STATUS_AUTHORISED) {
                $this->is_synced = 3;
            }

            return $this->save();
        }
    }

    public function xeroLogKey()
    {
        return (new self)->getMorphClass().'_'.($this->isInitial() ? 'i_' : '').$this->student_id.'_'.$this->course_id;
    }

    /*protected static function boot()
    {
        parent::boot();
        self::observe(StudentsObserver::class);
    }*/

    public function getFormattedInvoiceNumberAttribute()
    {
        // return 'GAL-' . $this->invoice_number . '-' .$this->id;
        // return 'GAL-' . $this->student->generated_stud_id .'-'. $this->course->course_code .'-AG'. $this->agent_id .'-'. $this->invoice_number;
        $keys = [
            'GAL',
            ! empty($this->student->generated_stud_id) ? $this->student->generated_stud_id : null,
            ! empty($this->course->course_code) ? $this->course->course_code : null,
            ! empty($this->agent_id) ? 'AG'.$this->agent_id : null,
            $this->invoice_number,
        ];
        $keys = array_filter($keys, fn ($value) => ! is_null($value) && $value !== '');

        return implode('-', $keys);
    }
}
