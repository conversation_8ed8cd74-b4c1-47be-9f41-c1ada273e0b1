<?php

namespace App\ProcessTask\Course;

use App\Model\v2\CourseTemplate;
use App\ProcessTask\Course\Payloads\AddUnitToCoursePayload;
use Closure;

class AssignToTemplate
{
    public function handle(AddUnitToCoursePayload $payload, Closure $next): AddUnitToCoursePayload
    {
        $template = $payload->template;
        if (empty($template) || empty($template->id)) {
            $template = CourseTemplate::getTemplateData($payload->course, 0, true);
        }
        if ($payload->subject) {
            if ($payload->unitToSave->Added) {
                $template->mapSubjectToTemplate($payload->subject, true);
            } else {
                $template->removeSubject($payload->subject);
            }
        }
        $payload->template = $template;

        return $next($payload);
    }
}
