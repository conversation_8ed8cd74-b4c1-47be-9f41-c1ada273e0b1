<template>
    <div class="space-y-2 border-b border-gray-200">
        <div class="px-4 py-6">
            <FormSectionTitle
                :isProfile="isProfile"
                :text="'Course Templates'"
                :info="true"
                :hasBottomBorder="false"
            />
            <div class="mb-2 text-gray-500">
                Majors are the set of {{ isHigherEd ? 'subjects' : 'units' }} which define the
                number of core {{ isHigherEd ? 'subjects' : 'units' }} and number of elective
                {{ isHigherEd ? 'subjects' : 'units' }} that are to be enrolled to the students.
                Major may have different number and set of
                {{ isHigherEd ? 'subjects' : 'units' }} within a same course. Though the core
                {{ isHigherEd ? 'subjects' : 'units' }} will remain same over all the majors in a
                course.
            </div>
            <div>
                <Button @click="createNewTemplateBtn" class="w-full">
                    <div class="flex w-auto items-center">
                        <icon :name="'plus'" :width="16" :height="16" :fill="'white'" />
                        <div class="ml-2">New Major</div>
                    </div>
                </Button>
            </div>
        </div>
    </div>
    <div class="space-y-4 px-4 py-6">
        <IconInput v-model="templatesSearchText" :className="'w-full'" :placeholder="'Search'" />
        <div class="-mx-4">
            <GlobalContextLoader :context="'loading-template-units'" :overlay="true">
                <div
                    v-for="(template, index) in getAllTemplates"
                    :key="index"
                    class="flex cursor-pointer items-center justify-between px-4 py-2 hover:bg-primary-blue-100"
                    :class="{
                        'bg-primary-blue-50 text-primary-blue-500': template.id === selected.id,
                    }"
                    @click="loadNewTemplate(template)"
                >
                    {{ template.template_name }}
                    <div v-if="!template.is_master_template">
                        <dropdown-menu
                            :items="templateActions"
                            :icon="actionIcon"
                            :variant="'action'"
                            :width="width"
                            @itemClick.stop="handleActionsClick($event, template)"
                            :autoHide="false"
                        ></dropdown-menu>
                    </div>
                </div>
            </GlobalContextLoader>
        </div>
    </div>
</template>
<script>
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import IconInput from '@spa/components/IconInput.vue';
import { moreHorizontalIcon } from '@progress/kendo-svg-icons';
import ActionMenu from '@spa/components/KendoGrid/ActionMenu.vue';
import Button from '@spa/components/Buttons/Button.vue';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';

export default {
    props: {
        isProfile: { type: Boolean, default: false },
        templates: { type: [Array, Object], default: [] },
        selected: { type: [Array, Object], default: [] },
    },
    components: {
        PrimaryButton,
        IconInput,
        'dropdown-menu': ActionMenu,
        Button,
        GlobalContextLoader,
    },
    data() {
        return {
            width: 40,
            actionIcon: moreHorizontalIcon,
            templatesSearchText: '',
            filteredTemplates: '',
            templateActions: [
                {
                    text: 'Duplicate this template',
                    value: 'copy',
                    icon: 'copy',
                },
                {
                    text: 'Delete template',
                    value: 'delete',
                    icon: 'delete',
                },
            ],
        };
    },
    emits: ['createnew', 'load'],
    computed: {
        ...mapState(useCoursesStore, ['course']),
        isHigherEd() {
            return this.isCourseHigherEd(this.course?.course_type_id || null);
        },
        getAllTemplates() {
            if (this.templatesSearchText.length > 0) {
                return this.templates.filter((item) =>
                    item.template_name.includes(this.templatesSearchText)
                );
            }
            return this.templates;
        },
    },
    methods: {
        createNewTemplateBtn() {
            this.$emit('createnew');
        },
        loadNewTemplate(template) {
            this.$emit('load', template);
        },
        handleActionsClick(e, dataItem) {
            switch (e.target.closest('[data-item]').dataset.item) {
                case 'copy':
                    this.$emit('duplicate', dataItem);
                    break;
                case 'delete':
                    this.$emit('delete', dataItem);
                    break;
            }
        },
    },
    watch: {
        templatesSearchText: function (newVal, oldVal) {
            // Assign the new value correctly to this.templatesSearchText
            this.templatesSearchText = newVal;
            // Ensure it's not an infinite loop by checking if the value has actually changed
            if (newVal !== oldVal) {
                //this.getTemplatesList();
            }
        },
    },
};
</script>
