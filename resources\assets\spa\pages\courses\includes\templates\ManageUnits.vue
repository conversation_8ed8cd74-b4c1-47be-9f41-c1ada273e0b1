<template>
    <div class="space-y-4">
        <div class="space-y-4">
            <CourseUnitStatus
                :selectedCore="selectedCore"
                :selectedElective="selectedElective"
                :currentTemplate="currentTemplate"
            />
            <div class="flex flex-wrap gap-2 space-x-0 xl:gap-0 xl:space-x-4" v-if="hasVacancies">
                <Button :size="'sm'" :variant="primary" @click="handleUnitsPrimaryBtn">
                    <div class="flex w-auto items-center">
                        <icon :name="'plus'" :width="16" :height="16" :fill="'white'" />
                        <div class="ml-2">
                            {{
                                addUnitsFromTga
                                    ? 'New Unit of Competency'
                                    : isHigherEd
                                      ? 'Add New Subject'
                                      : 'Add New Unit'
                            }}
                        </div>
                    </div>
                </Button>
                <Button
                    :size="'sm'"
                    :variant="'secondary'"
                    v-if="addUnitsFromTga"
                    @click="addUnitManually"
                    class="w-auto"
                >
                    <div class="flex w-auto items-center">
                        <icon :name="'plus'" :width="16" :height="16" />
                        <div class="ml-2">Add {{ isHigherEd ? 'Subject' : 'Unit' }} Manually</div>
                    </div>
                </Button>
                <Button
                    :size="'sm'"
                    :variant="'secondary'"
                    v-if="CourseNeedsTemplates"
                    @click="copyUnitsFromTemplate"
                >
                    <div class="flex w-auto items-center">
                        <icon :name="'plus'" :width="16" :height="16" />
                        <div class="ml-2">
                            Add {{ isHigherEd ? 'Subjects' : 'Units' }} From Other Template
                        </div>
                    </div>
                </Button>
            </div>
            <div v-else>
                <div class="flex items-center gap-2 rounded-md bg-green-100 p-2 text-sm">
                    <icon :name="'tickmark'" :width="16" :height="16" :fill="'green'" />
                    <div class="text-green-700">
                        You have reached the maximum number of
                        {{ isHigherEd ? 'subjects' : 'units' }} for this major.
                    </div>
                </div>
            </div>
            <div
                class="UnitsAdded rounded-md border border-gray-200 px-4 py-2"
                v-if="
                    selectedUnits.units.length > 0 ||
                    (currentTemplate.is_master_template && selectedUnits.orphans.length)
                "
            >
                <div class="grid grid-cols-12 gap-4 rounded-md bg-gray-100 py-1">
                    <div class="col-span-7 text-center uppercase text-gray-500">Unit Name</div>
                </div>
                <draggable
                    class="tw-drag-group__list"
                    tag="transition-group"
                    :component-data="{
                        tag: 'div',
                        type: 'transition-group',
                        name: !drag ? 'flip-list' : null,
                        // type: 'transition',
                        // name: 'flip-list'
                    }"
                    v-model="selectedUnits.units"
                    v-bind="dragOptions"
                    :move="checkMove"
                    @start="drag = true"
                    @end="handleSorted"
                    item-key="id"
                >
                    <template #item="{ element }">
                        <div
                            class="tw-drag-group__list-item w-full items-center border-b bg-white py-2 text-sm"
                            :key="element.unit_details.id ?? 0"
                        >
                            <UnitListItem
                                :unit="element"
                                :type="
                                    element.IsEssential == true || element.Type == 'Core'
                                        ? 'core'
                                        : 'elective'
                                "
                                @editunit="editUnit"
                                @deleteunit="deleteUnit"
                                @editsubject="editSubject"
                                @deletefromtemplate="deleteSubjectFromTemplate"
                                @deletesubject="deleteSubject"
                            />
                        </div>
                    </template>
                </draggable>
                <template
                    v-for="(unit, index) in selectedUnits?.orphans"
                    :key="index"
                    v-if="currentTemplate.is_master_template"
                >
                    <div class="w-full items-center border-b py-2 text-sm" v-if="unit.Added">
                        <UnitListItem
                            :unit="unit"
                            :type="
                                unit.IsEssential == true || unit.Type == 'Core'
                                    ? 'core'
                                    : 'elective'
                            "
                            @editunit="editUnit"
                            @deleteunit="deleteUnit"
                        />
                    </div>
                </template>
            </div>
            <div
                class="UnitsAdded rounded-md border border-gray-200 p-4 text-center text-sm"
                v-else
            >
                This major does not have any units assigned to it yet.
            </div>
        </div>
    </div>
    <transition name="fade">
        <div v-if="editUnitsVisible">
            <editunit
                :visible="editUnitsVisible"
                :template="currentTemplate"
                :course="getCourseData"
                :courseSubjects="getAllSubjects"
                :unit="getUnitData"
                :loading="unitsLoading"
                :nominalhours="nominalHours"
                :custom="customUnit"
                @closed="closeUnitEditWindow"
            />
        </div>
    </transition>
    <transition name="fade">
        <div v-if="selectUnitsVisible">
            <selectunits
                :visible="selectUnitsVisible"
                :courseUnits="getUnits"
                @added="updateCourses"
                @adding="saveCourses"
                @closed="closeWindow"
                :process="process"
            />
        </div>
    </transition>
</template>
<script>
import draggable from 'vuedraggable';
import useConfirm from '@spa/services/useConfirm';
import EditUnit from '@spa/pages/courses/includes/templates/EditUnits.vue';
import SelectUnits from '@spa/pages/courses/includes/templates/SelectUnits.vue';
import Button from '@spa/components/Buttons/Button';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import SecondaryButton from '@spa/components/Buttons/SecondaryButton.vue';
import { courseSubjectsDropdownData, unitEditData } from '@spa/services/courseFormResource';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import Card from '@spa/components/Card/Card.vue';
import globalHelper from '@spa/plugins/global-helper';
import AssignSubjectsBulk from '@spa/pages/courses/includes/AssignSubjectsBulk.vue';
import UnitListItem from './UnitListItem.vue';
import CourseUnitStatus from '@spa/pages/courses/includes/templates/CourseUnitStatus.vue';

export default {
    setup(_, { emit }) {
        const store = useCoursesStore();
        const confirm = useConfirm();
        const deleteUnit = (unit) => {
            confirm.require({
                message: 'Are you sure you want to remove selected unit?',
                header: 'Confirmation',
                icon: 'pi pi-exclamation-triangle',
                accept: async () => {
                    const unitsData = {
                        id: unit.unit_details?.id || null,
                        course: unit.unit_details?.course_id,
                        template: store.currentTemplate?.id || null,
                    };
                    $http.post(route('spa.courses.deleteunit'), unitsData).then((resp) => {
                        if (resp.success) {
                            emit('deleted', resp);
                        }
                    });
                },
                reject: () => {
                    // Callback to execute when user rejects the action
                    // You can add any logic here if needed
                },
                onHide: () => {
                    // Callback to execute when dialog is hidden
                    // You can add any logic here if needed
                },
            });
        };
        return {
            deleteUnit,
            store,
        };
    },
    props: {
        currentTemplate: { type: [Array, Object], default: [] },
        selectedUnits: { type: [Array, Object], default: [] },
        subjects: { type: [Array, Object], default: [] },
        consumer: { type: String, default: 'settings' }, //will be templates/settings to identify call from templates or course settings
    },
    emits: ['new', 'sort', 'deleted', 'addingunit'],
    components: {
        editunit: EditUnit,
        selectunits: SelectUnits,
        PrimaryButton,
        Button,
        draggable,
        Card,
        AssignSubjectsBulk,
        UnitListItem,
        CourseUnitStatus,
    },
    data() {
        return {
            editUnitsVisible: false,
            unitsLoading: false,
            selectUnitsVisible: false,
            customUnit: false,
            unitData: [],
            nominalHours: [],
            closeAfterSave: true,
            process: null,
            copyTemplateUnitsWindow: false,
        };
    },
    computed: {
        course() {
            return this.store.course || {};
        },
        dragOptions() {
            return {
                animation: 200,
                group: 'description',
                disabled: false,
                ghostClass: 'tw-drag-group__ghost',
            };
        },
        addUnitsFromTga() {
            const courseType = this.course?.course_type_id;
            return this.isCourseShortCourse(courseType) || this.isCourseVet(courseType);
        },
        selectedCore: function () {
            return this.selectedUnits.core_added || 0;
            if (!this.selectedUnits?.core) return 0;
            this.coreCount = 0;
            this.selectedUnits?.core.forEach((unit) => {
                // Set the initial value based on unit.Added
                if (unit.Added) {
                    this.coreCount++;
                }
            });
            return this.coreCount;
        },
        selectedElective: function () {
            return this.selectedUnits.elective_added || 0;
            if (!this.selectedUnits?.elective) return 0;
            this.electivesCount = 0;
            this.selectedUnits?.elective.forEach((unit) => {
                // Set the initial value based on unit.Added
                if (unit.Added) {
                    this.electivesCount++;
                }
            });
            return this.electivesCount;
        },
        getUnits: function () {
            let allUnits = this.selectedUnits;
            allUnits.core.forEach((unit) => {
                unit.Selected = unit.Added;
            });
            allUnits.elective.forEach((unit) => {
                unit.Selected = unit.Added;
            });
            return allUnits;
        },
        getCourseData() {
            return this.course || [];
        },
        getAllSubjects: function () {
            return this.subjects.map((sub) => courseSubjectsDropdownData(sub));
        },
        getUnitData: function () {
            if (!this.currentTemplate.is_master_template) {
                this.unitData['solo_subject'] = true;
            }
            return unitEditData(this.unitData, this.course.id, this.addUnitsFromTga);
        },
        CourseNeedsTemplates() {
            return !this.isCourseShortCourse(this.course.course_type_id || null);
        },
        isHigherEd() {
            return this.isCourseHigherEd(this.course.course_type_id || null);
        },
        hasVacancies() {
            return (
                this.currentTemplate.no_of_core_subject > this.selectedCore ||
                this.currentTemplate.no_of_elective_subject > this.selectedElective
            );
        },
    },
    methods: {
        handleUnitsPrimaryBtn() {
            if (this.addUnitsFromTga) {
                this.addUnits();
            } else {
                this.addUnitManually();
            }
        },
        addUnits() {
            this.selectUnitsVisible = true;
            /* 
                event triggred when add unit button is clicked so that we can save any changes made to template before hand
                */
            this.$emit('addingunit', true);
        },
        addUnitManually() {
            this.unitsLoading = false;
            this.editUnitsVisible = true;
            this.unitData = [];
            this.customUnit = true;
            /* 
                event triggred when add unit button is clicked so that we can save any changes made to template before hand
                */
            this.$emit('addingunit', true);
        },
        checkMove: function (evt) {
            const subjectName = evt.draggedContext.element.subject_name || '';
            return subjectName.length > 0;
        },
        handleSorted() {
            this.$emit('sort');
        },
        editUnit(unit) {
            //to the ajax to get the current unit
            this.unitsLoading = false;
            this.editUnitsVisible = true;
            if (unit?.unit_details.hasOwnProperty('data')) {
                this.unitData = unit?.unit_details.data;
            } else {
                this.unitData = unit?.unit_details;
            }
            this.nominalHours = {
                agreed: unit.nominal_hours || null,
                is_different: unit.nominal_hours_different || false,
            };
        },
        closeWindow() {
            this.selectUnitsVisible = false;
            this.$emit('addingunit', false);
        },
        closeUnitEditWindow() {
            this.editUnitsVisible = false;
            this.unitData = {};
            (this.nominalHours = {}), (this.customUnit = false);
        },
        addUnitToCourse(unitsData) {
            this.process = !this.closeAfterSave ? 'hold' : 'close';
            unitsData.core.forEach((unit) => {
                unit.Added = unit.Selected || false;
            });
            unitsData.elective.forEach((unit) => {
                unit.Added = unit.Selected || false;
            });
            const postData = {
                units: unitsData,
                template: this.currentTemplate,
            };
            $http.post(this.route('spa.courses.addunits'), postData).then((resp) => {
                if (resp.success) {
                    this.$emit('added', resp);
                    this.selectUnitsVisible = !this.closeAfterSave;
                }
                this.process = null;
            });
        },
        updateCourses(unitsData) {
            this.closeAfterSave = false;
            this.addUnitToCourse(unitsData);
        },
        saveCourses(unitsData) {
            this.closeAfterSave = true;
            this.addUnitToCourse(unitsData);
        },
        copyUnitsFromTemplate() {
            this.$emit('copyfromtemplate', true);
        },
    },
};
</script>
