<?php

namespace App\ProcessTask\Course;

use App\Classes\SiteConstants;
use App\Model\v2\CourseSubjects;
use App\Model\v2\Subject;
use App\Model\v2\SubjectUnits;
use App\ProcessTask\Course\Payloads\AddUnitToCoursePayload;
use Closure;

class FindOrCreateSubject
{
    public function handle(AddUnitToCoursePayload $payload, Closure $next): AddUnitToCoursePayload
    {
        if ($payload->unitToSave->Added) {
            // add the unit
            $this->addSubjectAndUnit($payload);
        } else {
            // dump("Remove sub", $payload->unitToSave, $payload);
            // remove the unit if added
            $this->removeSubjectAndUnit($payload);
        }

        return $next($payload);
    }

    private function addSubjectAndUnit(AddUnitToCoursePayload $payload)
    {

        $subjectType = $payload->unitToSave->Type ?? null;
        $masterUnitType = $payload->masterUnit->unit_type ?? null;
        $subjectType = $subjectType ?? $masterUnitType ?? SiteConstants::ELECTIVETEXT;
        $subjectType = $subjectType == SiteConstants::CORETEXT ? SiteConstants::CORETEXT : SiteConstants::ELECTIVETEXT;

        $subjectData = [
            'college_id' => $payload->course->college_id ?? null,
            'course_id' => $payload->course->id ?? null,
            'subject_code' => $payload->masterUnit->unit_code ?? null,
            'subject_name' => $payload->masterUnit->unit_name ?? null,
            'subject_type' => $subjectType,
            'contact_hours' => $payload->masterUnit->nominal_hours ?? null,
            'subject_fee' => $payload->masterUnit->tution_fees ?? null,
            'domestic_subject_fee' => $payload->masterUnit->domestic_tution_fees ?? null,
            'delivery_mode' => $payload->masterUnit->delivery_mode ?? null,
            'internal' => $payload->masterUnit->internal ?? null,
            'external' => $payload->masterUnit->external ?? null,
            'workplace_based_delivery' => $payload->masterUnit->workplace_based_delivery ?? null,
            'EFTSL_study_load' => $payload->masterUnit->EFTSL_study_load ?? null,
            'is_active' => $payload->masterUnit->status ?? null,
        ];
        /* get the master subject */
        $masterSubject = Subject::where('subject_code', $subjectData['subject_code'])->first();
        if (! $masterSubject) {
            $masterSubject = new Subject($subjectData);
            $masterSubject->save();
        }
        $subjectData['synced_subject'] = $masterSubject->id;
        /* check if the course has subject */
        $subject = CourseSubjects::where([
            'course_id' => $subjectData['course_id'],
            'synced_subject' => $subjectData['synced_subject'],
        ])->first();
        if (! $subject) {
            $subject = new CourseSubjects($subjectData);
            $subject->saveQuietly();
        }
        $payload->subject = $subject;
        /* now add unit to the subject */
        $unitData = $payload->masterUnit->toArray();

        $unitData['course_subject_id'] = $subject->id;
        $unitData['course_id'] = $payload->course->id;
        $unitData['unit_type'] = $subjectType;
        $unitData['unit_id'] = $unitData['id'];
        /* find subject unit */
        $subjectUnit = $subject->subject_units()->where([
            'unit_id' => $unitData['unit_id'],
        ])->first();
        if (! $subjectUnit) {
            $subjectUnit = new SubjectUnits($unitData);
            $subjectUnit->saveQuietly();
        }
        $payload->subjectUnit = $subjectUnit;

        return $payload;
    }

    private function removeSubjectAndUnit(AddUnitToCoursePayload $payload)
    {
        $subject = CourseSubjects::with(
            [
                'subject_unit' => function ($query) use ($payload) {
                    $query->where(['unit_id' => $$payload->masterUnit->id]);
                },
            ]
        )->where([
            'course_id' => $payload->course->id,
            'subject_code' => $payload->masterUnit->unit_code,
        ])->first();
        if ($subject) {
            $subjectUnit = $subject->subject_unit ?? null;

            $payload->subject = $subject;
            $payload->subjectUnit = $subjectUnit;

            foreach ($subjectUnit as $unit) {
                $unit->deleteQuietly();
            }
            $subject->deleteQuietly();
        }

        return $payload;
    }
}
