<template>
    <div class="mb-3 justify-between">
        <div class="flex space-x-4">
            <div class="w-1/6">
                <input-group :label="`Total ${isHigherEd ? 'Subjects' : 'Units'}`">
                    <numericinput
                        :placeholder="`Total ${isHigherEd ? 'Subjects' : 'Units'}`"
                        :readonly="true"
                        :value="packaging.core + packaging.elective"
                        :spinners="false"
                        :step="1"
                        :min="0"
                        :max="100"
                        :disabled="true"
                    />
                </input-group>
            </div>
            <div class="w-1/6">
                <input-group :label="`Total Core ${isHigherEd ? 'Subjects' : 'Units'}`">
                    <numericinput
                        :placeholder="`Total Core ${isHigherEd ? 'Subjects' : 'Units'}`"
                        v-model.lazy="packaging.core"
                        v-debounce="500"
                        :spinners="true"
                        :step="1"
                        :min="0"
                        :max="100"
                        @change="canSave++"
                    />
                </input-group>
            </div>
            <div class="w-1/6">
                <input-group :label="`Total Elective ${isHigherEd ? 'Subjects' : 'Units'}`">
                    <numericinput
                        :placeholder="`Total Elective ${isHigherEd ? 'Subjects' : 'Units'}`"
                        v-model.lazy="packaging.elective"
                        v-debounce="500"
                        :spinners="true"
                        :step="1"
                        :min="0"
                        :max="100"
                        @change="canSave++"
                    />
                </input-group>
            </div>
            <div class="flex w-1/6 pb-4">
                <Button
                    type="button"
                    @click="updatePackaging"
                    :size="'sm'"
                    :variant="'primary'"
                    :loading="packageSaving"
                    :loadingText="'Saving...'"
                    :disabled="packageSaving || canSave < 1"
                    :pt="{ root: 'mt-auto' }"
                >
                    <span class="px-6 text-sm leading-4 text-white"> Save </span>
                </Button>
            </div>
        </div>
    </div>
</template>
<script>
import { NumericTextBox } from '@progress/kendo-vue-inputs';
import { useCoursesStore } from '@spa/stores/modules/courses';
import { mapState } from 'pinia';
import Button from '@spa/components/Buttons/Button';
import { debounce } from 'lodash';

export default {
    components: {
        numericinput: NumericTextBox,
        Button,
    },
    data() {
        return {
            packaging: {
                core: 0,
                elective: 0,
            },
            holdUpdate: false,
            packageSaving: false,
            canSave: 0,
        };
    },
    mounted() {
        this.packaging = {
            core: this.course?.core_units_number || 0,
            elective: this.course?.elective_units_number || 0,
        };
        setTimeout(() => {
            this.packageSaving = false;
            this.canSave = 0;
        }, 2);
    },
    computed: {
        ...mapState(useCoursesStore, [
            'course',
            'updateCoursePackaging',
            'setCourseProgress',
            'setTemplates',
            'setCurrentTemplate',
            'setUnitVariables',
        ]),
        isHigherEd() {
            return this.isCourseHigherEd(this.course?.course_type_id || null);
        },
    },
    methods: {
        updatePackaging() {
            if (this.packageSaving || this.canSave < 1) return false;
            let formData = this.packaging;
            formData.id = this.course?.id || 0;
            this.packageSaving = true;
            this.holdUpdate = true;
            $http
                .post(this.route('spa.courses.packaging'), formData)
                .then((resp) => {
                    if (resp.success) {
                        this.packaging = resp.packaging;
                        this.updateCoursePackaging(resp.packaging);
                        this.setCourseProgress(resp.progress);
                        this.setCurrentTemplate(resp.template);
                        this.setUnitVariables(resp.units);
                    }
                })
                .catch((error) => {
                    console.log('Form saving process cancelled');
                })
                .finally(() => {
                    setTimeout(() => {
                        this.packageSaving = false;
                    }, 2);
                });
        },
    },
};
</script>
