<?php

namespace App\ProcessTask\Course;

use App\Model\v2\Units;
use App\ProcessTask\Course\Payloads\AddUnitToCoursePayload;
use Closure;

class FindOrCreateUnit
{
    public function handle(AddUnitToCoursePayload $payload, Closure $next): AddUnitToCoursePayload
    {
        /* get units data from the master units collection table */
        $unitData = $payload->unitToSave;
        $unitCode = $unitData->Code ?? $unitData->unit_code ?? null;
        if (! $unitCode) {
            throw new \Exception('Unit code is required');
        }
        $unit = Units::where('unit_code', $unitCode)->first();
        if (! $unit) {
            $unitData = $unitData->toArray();
            $unitData = $unitData['unit_details'] ?? $unitData ?? [];
            $unit = Units::createNewMasterRecord($unitData);
        }
        if (! $unit) {
            throw new \Exception('Unable to prepare master unit record.');
        }
        $payload->masterUnit = $unit;

        return $next($payload);

    }
}
