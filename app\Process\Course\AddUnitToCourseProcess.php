<?php

namespace App\Process\Course;

use App\ProcessTask\Course\AssignToTemplate;
use App\ProcessTask\Course\FindOrCreateSubject;
use App\ProcessTask\Course\FindOrCreateUnit;
use App\ProcessTask\Course\SyncToLegacy;
use Support\Traits\ProcessTasks;

class AddUnitToCourseProcess
{
    use ProcessTasks;

    public $tasks = [
        /* Find or create the unit */
        FindOrCreateUnit::class,
        /* Find or create the subject */
        FindOrCreateSubject::class,
        /* Assign the unit to the subject */
        AssignToTemplate::class,
        /* Sync the unit to the legacy course tables */
        SyncToLegacy::class,
    ];

    public function useSomeSaperateTask()
    {
        $this->tasks = [];

        return $this;
    }
}
