<?php

namespace App\ProcessTask\Course;

use App\Model\v2\CourseTemplate;
use App\ProcessTask\Course\Payloads\AddUnitToCoursePayload;

class FindOrCreateTemplate
{
    public function handle(AddUnitToCoursePayload $payload): AddUnitToCoursePayload
    {
        /* this method will find the template requested */
        /* it will create 1 if the course doesnot have one default */
        $template = $payload->template;

        if (gettype($template) === 'integer') {
            $template = CourseTemplate::getTemplateData($payload->course->id, $template);
        }
        if (empty($template) || empty($template->id)) {
            $templatesList = CourseTemplate::getTemplatesList($payload->course->id, 'id');

            $defaultTemplate = array_filter($templatesList, function ($template) {
                return $template->is_master_template == 1;
            });

            if (! empty($templatesList) && ! $defaultTemplate) {
                $defaultTemplate = $templatesList;
            }
            $template = $defaultTemplate[0] ?? [];
        }
        $payload->template = $template;

        return $payload;
    }
}
