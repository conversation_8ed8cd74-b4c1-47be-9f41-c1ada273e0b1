<template>
    <div class="items-center text-sm">This course should include {{ getSummaryText }}.</div>
    <div class="flex items-center space-x-4">
        <div class="text-sm">You have added</div>
        <div class="rounded-md bg-primary-blue-100 px-2 py-2 text-xs text-primary-blue-800">
            {{ selectedCoreText }} {{ isHigherEd ? 'Subjects' : 'Units' }}
        </div>
        <div class="rounded-md bg-gray-100 px-2 py-2 text-xs text-gray-800">
            {{ selectedElectiveText }} {{ isHigherEd ? 'Subjects' : 'Units' }}
        </div>
    </div>
    <div
        v-if="currentTemplate.is_master_template && CourseNeedsTemplates"
        class="text-md rounded-md bg-orange-100 p-2 italic text-orange-700"
    >
        This is a master template, All the {{ isHigherEd ? 'subjects' : 'units' }} added to this
        course will be listed here regardless of any majors.
        {{ isHigherEd ? 'Subjects' : 'Units' }} after they are assigned to a subject can then be
        added to other majors.
    </div>
</template>
<script>
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';

export default {
    name: 'CourseUnitStatus',
    props: {
        selectedCore: {
            type: Number,
            required: true,
        },
        selectedElective: {
            type: Number,
            required: true,
        },
        currentTemplate: {
            type: Object,
            required: true,
        },
    },
    computed: {
        ...mapState(useCoursesStore, ['course', 'selectedUnits']),
        isHigherEd() {
            return this.isCourseHigherEd(this.course?.course_type_id || null);
        },
        selectedCoreText() {
            const selected = this.selectedCore;
            //const total = this.course.core_units_number;
            const total = this.currentTemplate.no_of_core_subject;
            return total > 0 ? selected + '/' + total : selected;
        },
        selectedElectiveText() {
            const selected = this.selectedElective;
            //const total = this.course.elective_units_number;
            const total = this.currentTemplate.no_of_elective_subject;
            return total > 0 ? selected + '/' + total : selected;
        },
        CourseNeedsTemplates() {
            return !this.isCourseShortCourse(this.course.course_type_id || null);
        },
        getSummaryText: function () {
            /*
                const core = this.course.core_units_number;
                const elective = this.course.elective_units_number;
                */
            const core = this.currentTemplate.no_of_core_subject || 0;
            const elective = this.currentTemplate.no_of_elective_subject || 0;
            const total = core + elective;
            return `${total} ${this.pluralizeWord(
                this.isHigherEd ? 'subject' : 'unit',
                total
            )} - ${core} core ${this.pluralizeWord(
                this.isHigherEd ? 'subject' : 'unit',
                core
            )} & ${elective} elective ${this.pluralizeWord(
                this.isHigherEd ? 'subject' : 'unit',
                elective
            )}`;
        },
    },
};
</script>
