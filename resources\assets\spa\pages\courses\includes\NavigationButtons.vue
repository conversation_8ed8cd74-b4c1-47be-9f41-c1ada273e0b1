<template>
    <div
        class="mt-4 flex justify-end space-x-4"
        v-if="operationMode == 'profile' && isFormChanged == true"
    >
        <button type="submit" class="tw-btn-primary px-6" @click="checkFormErrors">
            <ContextLoader
                :context="'buttonLoading'"
                template="spin"
                :loaderFill="'#FFF'"
                :loaderStroke="'#1890ff'"
                :pt="{
                    root: 'flex items-center justify-center gap-1',
                }"
                v-if="saving"
            />
            <span class="text-sm font-medium leading-6 text-white">{{
                saving ? 'Saving' : 'Save'
            }}</span>
        </button>
    </div>
    <div
        class="courses-navigation-buttons absolute bottom-0 left-0 right-0 z-20"
        v-else-if="operationMode != 'profile'"
    >
        <div class="flex w-full justify-between border-t border-gray-100 bg-white px-8 py-3 shadow">
            <div>
                <button
                    type="button"
                    class="tw-btn-secondary cursor-pointer px-6"
                    @click="back"
                    v-if="getcurrentpos > 0 || saving"
                >
                    <span class="text-sm font-medium leading-6 text-gray-700">{{
                        saving ? 'Cancel' : 'Back'
                    }}</span>
                </button>
            </div>
            <div class="">
                <div v-if="hasSubmit" class="flex space-x-4">
                    <button
                        type="submit"
                        class="min-w-[110px] cursor-pointer"
                        @click="saveNormal"
                        :class="{
                            'tw-btn-secondary': showActivateButton,
                            'tw-btn-primary': !showActivateButton,
                        }"
                    >
                        <div class="flex items-center space-x-2">
                            <icon
                                :name="'loading'"
                                :width="16"
                                :height="16"
                                :fill="'#1890FF'"
                                :stroke="'#E2E8F0'"
                                v-if="savingDraft"
                            />
                            <span
                                class="text-sm font-medium leading-6"
                                :class="{
                                    'text-gray-700': showActivateButton,
                                    'text-white': !showActivateButton,
                                }"
                                >{{ buttonText }}</span
                            >
                        </div>
                    </button>
                    <button
                        type="submit"
                        class="tw-btn-primary min-w-[110px] cursor-pointer"
                        v-if="showActivateButton"
                        :disabled="isActivateBtnDisabled"
                        @click="saveActivate"
                    >
                        <div class="flex items-center space-x-2" @click="handleClick">
                            <icon
                                :name="'loading'"
                                :width="16"
                                :height="16"
                                :fill="'#1890FF'"
                                :stroke="'#E2E8F0'"
                                v-if="savingActive"
                            />
                            <span class="text-sm font-medium leading-6 text-white">{{
                                buttonActivateText
                            }}</span>
                        </div>
                    </button>
                </div>
                <div @click="next" class="tw-btn-primary cursor-pointer px-6" v-else>
                    <div class="flex items-center space-x-2">
                        <icon
                            :name="'loading'"
                            :width="16"
                            :height="16"
                            :fill="'#1890FF'"
                            :stroke="'#E2E8F0'"
                            v-if="saving"
                        />
                        <span class="text-sm font-medium leading-6 text-white">{{
                            buttonText
                        }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <k-dialog
        :visibleDialog="showdialog"
        :hideOnOverlayClick="true"
        :fixedActionBar="false"
        :width="'40%'"
        @modalclose="showdialog = false"
        v-if="isActivateBtnDisabled"
    >
        <template #title>
            <div class="text-lg font-medium">This course can not be activated</div>
        </template>
        <template #content>
            <div class="font-medium text-gray-600">
                This course profile has some missing information that is required before the course
                goes public.
            </div>
            <div
                v-for="(message, index) in getProgressStatusMessages"
                :class="{ 'border-b': index !== getProgressStatusMessages.length - 1 }"
                class="py-2 text-gray-500"
                :key="index"
            >
                {{ `${index + 1} - ${message}` }}
            </div>
        </template>
    </k-dialog>
</template>
<script>
import { mapState } from 'pinia';
import { Button } from '@progress/kendo-vue-buttons';
import { useCoursesStore } from '@spa/stores/modules/courses';
import ContextLoader from '@spa/components/Loader/ContextLoader.vue';
import KendoDialog from '@spa/components/KendoModals/KendoDialog.vue';

export default {
    components: {
        kbutton: Button,
        ContextLoader,
        'k-dialog': KendoDialog,
    },
    props: {
        allowsave: Boolean,
    },
    inject: {
        kendoForm: { default: {} },
    },
    data() {
        return {
            showdialog: false,
        };
    },
    computed: {
        ...mapState(useCoursesStore, [
            'course',
            'currentposition',
            'setCurrentStep',
            'updateFormState',
            'formState',
            'totalUpdateTabs',
            'operationMode',
            'config',
            'setFormSaveState',
            'formSaveState',
            'courseProgress',
        ]),
        getcurrentpos: function () {
            return this.currentposition;
        },
        hasSubmit: function () {
            const currentTab = this.getConfig?.order[this.currentposition];
            return !(currentTab.name == 'units');
        },
        buttonText: function () {
            const text = this.currentposition < this.totalUpdateTabs - 1 ? 'Next' : 'Save Course';
            return this.saving && !this.savingActive ? 'Saving...' : text;
        },
        buttonActivateText: function () {
            const text =
                this.currentposition < this.totalUpdateTabs - 1 ? 'Next' : 'Save & Activate Now';
            return this.savingActive ? 'Saving & Activating' : text;
        },
        getConfig: function () {
            const activeTabs = this.config.order.filter((item) => item.show === true);
            return { tabs: activeTabs.length, order: activeTabs };
        },
        saving: function () {
            return this.formState === 1;
        },
        savingDraft: function () {
            return this.saving && this.formSaveState === 0;
        },
        savingActive: function () {
            return this.saving && this.formSaveState === 1;
        },
        isCourseActive: function () {
            const activeStatus = this.course?.activated_now || 0;
            return activeStatus == 1;
        },
        isAllCompleted: function () {
            //const completedSteps = this.getConfig?.order?.filter(step => step.status).length || 0;
            const incompleteSteps =
                this.getConfig?.order?.filter(
                    (step, index) => index !== this.currentposition && !step.status
                ).length || 0;
            return !(incompleteSteps > 0);
        },
        showActivateButton: function () {
            return this.currentposition >= this.totalUpdateTabs - 1 && !this.isCourseActive;
        },
        isActivateBtnDisabled: function () {
            return this.showActivateButton && !this.isAllCompleted;
        },
        isFormChanged: function () {
            return this.kendoForm.modified || this.kendoForm.touched;
        },
        getProgressStatusMessages() {
            return Object.values(this.courseProgress)
                .map((item) => item.fields)
                .flat();
        },
    },
    methods: {
        back: function () {
            if (this.formState == 1) {
                this.updateFormState(2);
                return;
            }
            const newPosition = this.currentposition > 0 ? this.currentposition - 1 : 0;
            this.setCurrentStep(newPosition);
            return false;
        },
        next: function () {
            const newPosition =
                this.currentposition < this.totalUpdateTabs
                    ? this.currentposition + 1
                    : this.currentposition;
            //the step is completed
            this.checkFormErrors();
            this.setCurrentStep(newPosition);
            return false;
        },
        checkFormErrors() {
            if (!this.kendoForm.valid && this.kendoForm.visited) {
                const firstElement = document.querySelector('.k-text-error');
                if (firstElement) {
                    firstElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start', // aligns top of element with top of viewport
                        inline: 'nearest',
                    });
                }
            }
            // Add offset after scrolling
        },
        saveNormal: function () {
            this.checkFormErrors();
            this.setFormSaveState(0);
        },
        saveActivate: function () {
            this.checkFormErrors();
            this.setFormSaveState(1);
        },
        handleClick() {
            if (!this.isActivateBtnDisabled) return true;
            this.showdialog = true;
        },
    },
};
</script>
