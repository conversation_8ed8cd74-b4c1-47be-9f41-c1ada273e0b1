<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class StudentInvoiceCredit extends Model
{
    use LogsActivity;

    protected $table = 'rto_student_invoice_credit';

    protected $fillable = [
        'id',
        'college_id',
        'student_id',
        'course_id',
        'initial_payment_detail_id',
        'credit_amount',
        'credit_date',
        'remarks',
        'created_by',
        'updated_by'
    ];

    protected static $logAttributes = [
        'college_id',
        'student_id',
        'course_id',
        'initial_payment_detail_id',
        'credit_amount',
        'credit_date',
        'remarks',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Invoice credit has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = $this->customLogKey();
    }

    public function customLogKey()
    {
        return (new self)->getMorphClass().'_'.$this->student_id.'_'.$this->course_id;
    }
}
