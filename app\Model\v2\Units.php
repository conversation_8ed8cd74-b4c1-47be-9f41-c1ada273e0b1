<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Support\Traits\CreaterUpdaterTrait;

class Units extends Model
{
    use CreaterUpdaterTrait;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'rto_units';

    protected $fillable = [
        'college_id',
        'unit_code',
        'vet_unit_code',
        'unit_name',
        'description',
        'unit_type',
        'field_education',
        'delivery_mode',
        'internal',
        'external',
        'workplace_based_delivery',
        'nominal_hours',
        'tution_fees',
        'domestic_tution_fees',
        'module_unit_flag',
        'vet_flag',
        'work_placement',
        'AVETMISS_Report',
        'status',
        'unit_type_basic',
        'unit_source',
        'unit_scope',
        'active_from',
        'is_superseded',
        'superseded_date',
        'superseded_by',
        'created_by',
        'updated_by',
    ];

    public function courses()
    {
        return null;
        // return $this->hasManyThrough(
        //     Courses::class,
        //     CourseSubject::class,
        //     'course_id', //foreign key in the CourseSubject table
        //     'id', //foreign key on Subject table
        //     'id', //local key in courses table
        //     'subject_id' //local key in CourseSubject
        // )
        // ->select(
        //     'rto_subject.*',
        //     'rto_course_subject.id AS map_id',
        //     'rto_course_subject.course_stage',
        //     'rto_course_subject.subject_type',
        //     'rto_course_subject.subject_fee',
        //     'rto_course_subject.domestic_subject_fee',
        //     'rto_course_subject.unit_type',
        //     'rto_course_subject.delivery_mode',
        //     'rto_course_subject.EFTSL_study_load',
        //     'rto_course_subject.contact_hours',
        //     'rto_course_subject.is_active',
        //     'rto_course_subject.inc_in_certificate',
        //     'rto_course_subject.display_order',
        // )
        // ->orderBy('rto_course_subject.display_order');
    }

    public static function import($data = [])
    {
        $unitCode = $data['unit_code'] ?? null;
        if ($unitCode) {
            $existingUnit = Units::where('unit_code', '=', $unitCode)->first();
            $existingId = $existingUnit->id ?? null;
            if (! $existingId) {
                $deliveryMode = $data['delivery_mode'] ?? 'NNN';
                $deliverModes = str_split($deliveryMode);
                $deliverMode_internal = $deliverModes[0] ?? 'N';
                $deliverMode_external = $deliverModes[1] ?? 'N';
                $deliverMode_wpd = $deliverModes[2] ?? 'N';
                $tuitionFees = $data['tution_fees'] ?? 0;
                $domesticTuitionFees = $data['domestic_tution_fees'] ?? $tuitionFees;
                $importData = [
                    'college_id' => $data['college_id'] ?? 0,
                    'unit_code' => $data['unit_code'] ?? '',
                    'vet_unit_code' => $data['vet_unit_code'] ?? '',
                    'unit_name' => $data['unit_name'] ?? '',
                    'description' => $data['description'] ?? '',
                    'unit_type' => $data['unit_type'] ?? 'None',
                    'field_education' => $data['field_education'] ?? null,
                    'delivery_mode' => $deliveryMode,
                    'internal' => $deliverMode_internal,
                    'external' => $deliverMode_external,
                    'workplace_based_delivery' => $deliverMode_wpd,
                    'nominal_hours' => $data['nominal_hours'] ?? 0,
                    'tution_fees' => $tuitionFees,
                    'domestic_tution_fees' => $domesticTuitionFees,
                    'module_unit_flag' => $data['module_unit_flag'] ?? null,
                    'vet_flag' => $data['vet_flag'] ?? 'No',
                    'work_placement' => $data['work_placement'] ?? 'No',
                    'AVETMISS_Report' => $data['AVETMISS_Report'] ?? null,
                    'status' => $data['status'] ?? 0,
                    'created_at' => $data['created_at'] ?? '',
                    'updated_at' => $data['updated_at'] ?? '',
                    'created_by' => $data['created_by'] ?? '',
                    'updated_by' => $data['updated_by'] ?? '',
                ];

                return Units::create($importData);
            } else {
                return $existingUnit;
            }
        }

        return false;
    }

    public static function saveNewUnitFromApi($data = [])
    {
        $college_id = Auth::user()->college_id ?? 0;
        $user_id = Auth::user()->id;
        $unitCode = $data['Code'] ?? null;
        $unitTitle = $data['Title'] ?? null;
        if ($unitCode && $unitTitle) {
            $unitType = $data['IsEssential'] ? 'Core' : 'Elective';
            $unitTypeBasic = $data['unit_type_basic'] ?? null;
            $unitSource = $data['unit_source'] ?? 'custom';
            $unitScope = $data['unit_scope'] ?? 'public';
            $unitData = [
                'college_id' => $college_id,
                'unit_code' => $unitCode,
                'unit_name' => $unitTitle,
                'unit_type' => $unitType,
                'unit_type_basic' => $unitTypeBasic,
                'unit_source' => $unitSource,
                'unit_scope' => $unitScope,
                'status' => 1,
                'created_at' => now(),
                'created_by' => $user_id,
            ];

            return self::create($unitData);
        }

        return false;
    }

    public static function createNewMasterRecord($data)
    {
        /*
        default values for the fields
        */
        $source = $data['Source'] ?? 'custom';
        $defaultValues = [
            'college_id' => auth()->user()->college_id,
            'unit_type' => 'None',
            'internal' => 'N',
            'external' => 'N',
            'workplace_based_delivery' => 'N',
            'vet_flag' => 'No',
            'work_placement' => 'No',
            'unit_source' => $source,
            'unit_scope' => 'public',
            'is_superseded' => 0,
        ];
        $fillable = (new self)->getFillable();
        /*
        Assign the default values to the fields that have no values
        */
        $itmDta = [];
        foreach ($fillable as $value) {
            if (array_key_exists($value, $defaultValues) && empty($data[$value])) {
                $itmDta[$value] = $defaultValues[$value];
            } else {
                $itmDta[$value] = $data[$value] ?? null;
            }
        }
        $itmDta['status'] = 1;
        $itemDta = array_filter($itmDta);
        $unit = false;
        try {
            $unit = new Units($itemDta);
            $unit->save();
        } catch (\Exception $e) {
            $unit = false;
        } finally {
            return $unit;
        }
    }
}
